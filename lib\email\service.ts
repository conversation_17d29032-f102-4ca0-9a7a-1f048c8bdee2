import { render } from '@react-email/components'
import { sendEmail, EmailOptions } from './config'
import WelcomeEmail from './templates/welcome'
import ShipmentUpdateEmail from './templates/shipment-update'
import InvoiceEmail from './templates/invoice'
import { ShipmentStatus } from '@prisma/client'

export interface WelcomeEmailData {
  name: string
  email: string
  loginUrl?: string
}

export interface ShipmentUpdateEmailData {
  customerName: string
  customerEmail: string
  trackingNumber: string
  status: ShipmentStatus
  origin: string
  destination: string
  estimatedDelivery?: Date
  actualDelivery?: Date
  trackingUrl?: string
}

export interface InvoiceEmailData {
  customerName: string
  customerEmail: string
  invoiceNumber: string
  totalAmount: number
  currency: string
  dueDate: Date
  invoiceUrl?: string
  paymentUrl?: string
}

export class EmailService {
  static async sendWelcomeEmail(data: WelcomeEmailData): Promise<boolean> {
    try {
      const html = render(WelcomeEmail({
        name: data.name,
        email: data.email,
        loginUrl: data.loginUrl,
      }))

      const emailOptions: EmailOptions = {
        to: data.email,
        subject: 'Welcome to DCF Logistics!',
        html,
        text: `Welcome to DCF Logistics, ${data.name}! Your account has been created successfully.`,
      }

      return await sendEmail(emailOptions)
    } catch (error) {
      console.error('Failed to send welcome email:', error)
      return false
    }
  }

  static async sendShipmentUpdateEmail(data: ShipmentUpdateEmailData): Promise<boolean> {
    try {
      const html = render(ShipmentUpdateEmail({
        customerName: data.customerName,
        trackingNumber: data.trackingNumber,
        status: data.status,
        origin: data.origin,
        destination: data.destination,
        estimatedDelivery: data.estimatedDelivery,
        actualDelivery: data.actualDelivery,
        trackingUrl: data.trackingUrl,
      }))

      const statusText = data.status.replace('_', ' ')
      const emailOptions: EmailOptions = {
        to: data.customerEmail,
        subject: `Shipment Update: ${data.trackingNumber} - ${statusText}`,
        html,
        text: `Your shipment ${data.trackingNumber} status has been updated to: ${statusText}`,
      }

      return await sendEmail(emailOptions)
    } catch (error) {
      console.error('Failed to send shipment update email:', error)
      return false
    }
  }

  static async sendInvoiceEmail(data: InvoiceEmailData): Promise<boolean> {
    try {
      const html = render(InvoiceEmail({
        customerName: data.customerName,
        invoiceNumber: data.invoiceNumber,
        totalAmount: data.totalAmount,
        currency: data.currency,
        dueDate: data.dueDate,
        invoiceUrl: data.invoiceUrl,
        paymentUrl: data.paymentUrl,
      }))

      const emailOptions: EmailOptions = {
        to: data.customerEmail,
        subject: `New Invoice ${data.invoiceNumber} - $${data.totalAmount.toFixed(2)} ${data.currency}`,
        html,
        text: `You have a new invoice ${data.invoiceNumber} for $${data.totalAmount.toFixed(2)} ${data.currency}. Due date: ${data.dueDate.toLocaleDateString()}`,
      }

      return await sendEmail(emailOptions)
    } catch (error) {
      console.error('Failed to send invoice email:', error)
      return false
    }
  }

  static async sendPasswordResetEmail(email: string, name: string, resetUrl: string): Promise<boolean> {
    try {
      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Password Reset - DCF Logistics</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <img src="https://dcflogistics.com/logo.png" alt="DCF Logistics" style="height: 40px;">
            </div>
            
            <h1 style="color: #007cba; text-align: center;">Password Reset Request</h1>
            
            <p>Hello ${name},</p>
            
            <p>We received a request to reset your password for your DCF Logistics account.</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetUrl}" style="background-color: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Reset Password</a>
            </div>
            
            <p>If you didn't request this password reset, please ignore this email. Your password will remain unchanged.</p>
            
            <p>This link will expire in 1 hour for security reasons.</p>
            
            <p>Best regards,<br>The DCF Logistics Team</p>
            
            <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
            <p style="font-size: 12px; color: #666; text-align: center;">
              DCF Logistics | Banjul, The Gambia | +220 123 4567
            </p>
          </div>
        </body>
        </html>
      `

      const emailOptions: EmailOptions = {
        to: email,
        subject: 'Password Reset - DCF Logistics',
        html,
        text: `Hello ${name}, we received a request to reset your password. Click this link to reset: ${resetUrl}`,
      }

      return await sendEmail(emailOptions)
    } catch (error) {
      console.error('Failed to send password reset email:', error)
      return false
    }
  }

  static async sendContactFormEmail(data: {
    name: string
    email: string
    subject: string
    message: string
  }): Promise<boolean> {
    try {
      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>New Contact Form Submission - DCF Logistics</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h1 style="color: #007cba;">New Contact Form Submission</h1>
            
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <p><strong>Name:</strong> ${data.name}</p>
              <p><strong>Email:</strong> ${data.email}</p>
              <p><strong>Subject:</strong> ${data.subject}</p>
              <p><strong>Message:</strong></p>
              <div style="background-color: white; padding: 15px; border-radius: 4px; margin-top: 10px;">
                ${data.message.replace(/\n/g, '<br>')}
              </div>
            </div>
            
            <p style="font-size: 12px; color: #666;">
              This message was sent from the DCF Logistics website contact form.
            </p>
          </div>
        </body>
        </html>
      `

      const emailOptions: EmailOptions = {
        to: process.env.CONTACT_EMAIL || '<EMAIL>',
        subject: `Contact Form: ${data.subject}`,
        html,
        text: `New contact form submission from ${data.name} (${data.email}): ${data.message}`,
      }

      return await sendEmail(emailOptions)
    } catch (error) {
      console.error('Failed to send contact form email:', error)
      return false
    }
  }
}

export default EmailService
