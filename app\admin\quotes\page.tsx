"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { FileText, DollarSign, Clock, CheckCircle, Search, Mail, Phone, MapPin } from 'lucide-react'
import { toast } from 'sonner'

interface QuoteRequest {
  id: string
  fullName: string
  email: string
  phone: string
  company?: string
  serviceType: string
  origin: string
  destination: string
  cargoType: string
  cargoDetails: string
  weight?: string
  dimensions?: string
  quotedAmount?: number
  currency: string
  status: string
  validUntil?: string
  createdAt: string
  respondedAt?: string
}

export default function QuotesManagementPage() {
  const [quotes, setQuotes] = useState<QuoteRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [serviceFilter, setServiceFilter] = useState('all')
  const [selectedQuote, setSelectedQuote] = useState<QuoteRequest | null>(null)
  const [quoteAmount, setQuoteAmount] = useState('')
  const [quoteNotes, setQuoteNotes] = useState('')
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    quoted: 0,
    accepted: 0,
    totalValue: 0
  })

  useEffect(() => {
    fetchQuotes()
  }, [])

  const fetchQuotes = async () => {
    try {
      setLoading(true)
      // In production, this would fetch from the API
      // const response = await fetch('/api/admin/quotes')
      // const data = await response.json()
      
      // Mock data for demonstration
      const mockData = [
        {
          id: 'QUO-001',
          fullName: 'John Doe',
          email: '<EMAIL>',
          phone: '+************',
          company: 'ABC Trading',
          serviceType: 'Air Freight',
          origin: 'Banjul, Gambia',
          destination: 'Dakar, Senegal',
          cargoType: 'Electronics',
          cargoDetails: 'Laptops and mobile phones for retail',
          weight: '150 kg',
          dimensions: '120x80x60 cm',
          quotedAmount: 2500,
          currency: 'USD',
          status: 'QUOTED',
          validUntil: '2025-01-19T00:00:00Z',
          createdAt: '2024-12-19T10:30:00Z',
          respondedAt: '2024-12-19T14:15:00Z'
        },
        {
          id: 'QUO-002',
          fullName: 'Jane Smith',
          email: '<EMAIL>',
          phone: '+************',
          company: 'XYZ Logistics',
          serviceType: 'Ground Transportation',
          origin: 'Banjul, Gambia',
          destination: 'Bamako, Mali',
          cargoType: 'Textiles',
          cargoDetails: 'Cotton fabrics for manufacturing',
          weight: '500 kg',
          dimensions: '200x150x100 cm',
          currency: 'USD',
          status: 'PENDING',
          createdAt: '2024-12-18T15:45:00Z'
        },
        {
          id: 'QUO-003',
          fullName: 'Bob Wilson',
          email: '<EMAIL>',
          phone: '+************',
          serviceType: 'Sea Freight',
          origin: 'Banjul, Gambia',
          destination: 'Lagos, Nigeria',
          cargoType: 'Machinery',
          cargoDetails: 'Industrial equipment for construction',
          weight: '2000 kg',
          quotedAmount: 4800,
          currency: 'USD',
          status: 'ACCEPTED',
          validUntil: '2025-01-15T00:00:00Z',
          createdAt: '2024-12-17T09:15:00Z',
          respondedAt: '2024-12-17T16:30:00Z'
        }
      ]
      
      setQuotes(mockData)
      setStats({
        total: mockData.length,
        pending: mockData.filter(q => q.status === 'PENDING').length,
        quoted: mockData.filter(q => q.status === 'QUOTED').length,
        accepted: mockData.filter(q => q.status === 'ACCEPTED').length,
        totalValue: mockData.filter(q => q.quotedAmount).reduce((sum, q) => sum + (q.quotedAmount || 0), 0)
      })
    } catch (error) {
      console.error('Failed to fetch quotes:', error)
      toast.error('Failed to load quote requests')
    } finally {
      setLoading(false)
    }
  }

  const filteredQuotes = quotes.filter(quote => {
    const matchesSearch = quote.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quote.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quote.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || quote.status === statusFilter
    const matchesService = serviceFilter === 'all' || quote.serviceType === serviceFilter
    
    return matchesSearch && matchesStatus && matchesService
  })

  const updateQuoteStatus = async (quoteId: string, newStatus: string) => {
    try {
      setQuotes(prev => prev.map(quote => 
        quote.id === quoteId 
          ? { ...quote, status: newStatus }
          : quote
      ))
      toast.success('Quote status updated successfully')
    } catch (error) {
      toast.error('Failed to update quote status')
    }
  }

  const submitQuote = async () => {
    if (!selectedQuote || !quoteAmount) return

    try {
      const amount = parseFloat(quoteAmount)
      const validUntil = new Date()
      validUntil.setDate(validUntil.getDate() + 30)

      setQuotes(prev => prev.map(quote => 
        quote.id === selectedQuote.id 
          ? { 
              ...quote, 
              quotedAmount: amount, 
              status: 'QUOTED',
              validUntil: validUntil.toISOString(),
              respondedAt: new Date().toISOString()
            }
          : quote
      ))
      setQuoteAmount('')
      setQuoteNotes('')
      setSelectedQuote(null)
      toast.success('Quote sent successfully')
    } catch (error) {
      toast.error('Failed to send quote')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'QUOTED': return 'bg-blue-100 text-blue-800'
      case 'ACCEPTED': return 'bg-green-100 text-green-800'
      case 'REJECTED': return 'bg-red-100 text-red-800'
      case 'EXPIRED': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quote Management</h1>
          <p className="text-gray-600">Manage and respond to quote requests</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Quotes</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Quoted</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.quoted}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Accepted</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.accepted}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">${stats.totalValue.toLocaleString()}</div>
          </CardContent>
        </Card>
      </div>

      {/* Quotes List */}
      <Card>
        <CardHeader>
          <CardTitle>Quote Requests</CardTitle>
          <CardDescription>View and manage customer quote requests</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search quotes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="QUOTED">Quoted</SelectItem>
                <SelectItem value="ACCEPTED">Accepted</SelectItem>
                <SelectItem value="REJECTED">Rejected</SelectItem>
              </SelectContent>
            </Select>
            <Select value={serviceFilter} onValueChange={setServiceFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Service" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Services</SelectItem>
                <SelectItem value="Air Freight">Air Freight</SelectItem>
                <SelectItem value="Ground Transportation">Ground Transportation</SelectItem>
                <SelectItem value="Sea Freight">Sea Freight</SelectItem>
                <SelectItem value="Express Delivery">Express Delivery</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {loading ? (
            <div className="text-center py-8">Loading quotes...</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Quote ID</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Service</TableHead>
                  <TableHead>Route</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredQuotes.map((quote) => (
                  <TableRow key={quote.id}>
                    <TableCell className="font-mono text-sm">{quote.id}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{quote.fullName}</div>
                        <div className="text-sm text-gray-500">{quote.email}</div>
                      </div>
                    </TableCell>
                    <TableCell>{quote.serviceType}</TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{quote.origin}</div>
                        <div className="text-gray-500">→ {quote.destination}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {quote.quotedAmount ? (
                        <div className="font-medium">
                          ${quote.quotedAmount.toLocaleString()}
                        </div>
                      ) : (
                        <span className="text-gray-400">Pending</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(quote.status)}>
                        {quote.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{new Date(quote.createdAt).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm" onClick={() => setSelectedQuote(quote)}>
                            View
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-3xl">
                          <DialogHeader>
                            <DialogTitle>Quote Request - {quote.id}</DialogTitle>
                            <DialogDescription>
                              Submitted on {new Date(quote.createdAt).toLocaleString()}
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-6">
                            {/* Customer Information */}
                            <div>
                              <h3 className="text-lg font-semibold mb-3">Customer Information</h3>
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium">Name</label>
                                  <p>{quote.fullName}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Company</label>
                                  <p>{quote.company || 'N/A'}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Email</label>
                                  <p className="flex items-center gap-2">
                                    {quote.email}
                                    <Button variant="ghost" size="sm" asChild>
                                      <a href={`mailto:${quote.email}`}>
                                        <Mail className="h-4 w-4" />
                                      </a>
                                    </Button>
                                  </p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Phone</label>
                                  <p className="flex items-center gap-2">
                                    {quote.phone}
                                    <Button variant="ghost" size="sm" asChild>
                                      <a href={`tel:${quote.phone}`}>
                                        <Phone className="h-4 w-4" />
                                      </a>
                                    </Button>
                                  </p>
                                </div>
                              </div>
                            </div>

                            {/* Shipment Details */}
                            <div>
                              <h3 className="text-lg font-semibold mb-3">Shipment Details</h3>
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium">Service Type</label>
                                  <p>{quote.serviceType}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Cargo Type</label>
                                  <p>{quote.cargoType}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Origin</label>
                                  <p className="flex items-center gap-2">
                                    <MapPin className="h-4 w-4" />
                                    {quote.origin}
                                  </p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Destination</label>
                                  <p className="flex items-center gap-2">
                                    <MapPin className="h-4 w-4" />
                                    {quote.destination}
                                  </p>
                                </div>
                                {quote.weight && (
                                  <div>
                                    <label className="text-sm font-medium">Weight</label>
                                    <p>{quote.weight}</p>
                                  </div>
                                )}
                                {quote.dimensions && (
                                  <div>
                                    <label className="text-sm font-medium">Dimensions</label>
                                    <p>{quote.dimensions}</p>
                                  </div>
                                )}
                              </div>
                              
                              <div className="mt-4">
                                <label className="text-sm font-medium">Cargo Details</label>
                                <p className="whitespace-pre-wrap bg-gray-50 p-3 rounded mt-1">{quote.cargoDetails}</p>
                              </div>
                            </div>

                            {/* Quote Information */}
                            {quote.quotedAmount ? (
                              <div>
                                <h3 className="text-lg font-semibold mb-3">Quote Information</h3>
                                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                  <div className="flex justify-between items-center">
                                    <div>
                                      <p className="text-sm text-green-600">Quoted Amount</p>
                                      <p className="text-2xl font-bold text-green-800">
                                        ${quote.quotedAmount.toLocaleString()} {quote.currency}
                                      </p>
                                    </div>
                                    <div className="text-right">
                                      <p className="text-sm text-green-600">Valid Until</p>
                                      <p className="font-medium">
                                        {quote.validUntil ? new Date(quote.validUntil).toLocaleDateString() : 'N/A'}
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ) : quote.status === 'PENDING' && (
                              <div>
                                <h3 className="text-lg font-semibold mb-3">Provide Quote</h3>
                                <div className="space-y-4">
                                  <div>
                                    <label className="text-sm font-medium">Quote Amount (USD)</label>
                                    <Input
                                      type="number"
                                      value={quoteAmount}
                                      onChange={(e) => setQuoteAmount(e.target.value)}
                                      placeholder="Enter quote amount"
                                      min="0"
                                      step="0.01"
                                    />
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">Additional Notes</label>
                                    <Textarea
                                      value={quoteNotes}
                                      onChange={(e) => setQuoteNotes(e.target.value)}
                                      placeholder="Any additional notes or terms..."
                                      rows={3}
                                    />
                                  </div>
                                  <Button onClick={submitQuote} className="w-full">
                                    Send Quote
                                  </Button>
                                </div>
                              </div>
                            )}
                          </div>
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
          
          {filteredQuotes.length === 0 && !loading && (
            <div className="text-center py-8 text-gray-500">
              No quotes found matching your criteria
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
