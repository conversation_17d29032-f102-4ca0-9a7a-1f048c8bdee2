import type { <PERSON>ada<PERSON> } from "next"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  FileText,
  Plus,
  Search,
  Download,
  Eye,
  Edit,
  Send,
  DollarSign,
  Clock,
  AlertCircle,
  CheckCircle,
  Filter,
} from "lucide-react"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Invoicing System | DCF Logistics Admin",
  description: "Complete invoice management and billing system",
}

export default function InvoicingPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Invoicing System</h1>
          <p className="text-muted-foreground">Complete invoice management and billing control</p>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href="/admin/invoicing/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Invoice
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/admin/invoicing/templates">
              <FileText className="mr-2 h-4 w-4" />
              Templates
            </Link>
          </Button>
        </div>
      </div>

      {/* Invoice Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Invoices</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,247</div>
            <p className="text-xs text-muted-foreground">+12% from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Outstanding Amount</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$284,739</div>
            <p className="text-xs text-muted-foreground">23 pending invoices</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overdue Invoices</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">7</div>
            <p className="text-xs text-muted-foreground">$47,250 overdue</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Collection Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">94.2%</div>
            <p className="text-xs text-muted-foreground">+2.1% improvement</p>
          </CardContent>
        </Card>
      </div>

      {/* Invoice Management Tabs */}
      <Tabs defaultValue="all" className="space-y-4">
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="all">All Invoices</TabsTrigger>
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="overdue">Overdue</TabsTrigger>
            <TabsTrigger value="paid">Paid</TabsTrigger>
          </TabsList>

          <div className="flex gap-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input placeholder="Search invoices..." className="pl-8 w-64" />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* All Invoices Tab */}
        <TabsContent value="all">
          <Card>
            <CardHeader>
              <CardTitle>All Invoices</CardTitle>
              <CardDescription>Complete list of all invoices in the system</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Invoice #</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Service</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">INV-2025-001</TableCell>
                    <TableCell>Acme Corporation</TableCell>
                    <TableCell>Air Freight</TableCell>
                    <TableCell>$4,250.00</TableCell>
                    <TableCell>May 15, 2025</TableCell>
                    <TableCell>May 30, 2025</TableCell>
                    <TableCell>
                      <Badge variant="default">Paid</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell className="font-medium">INV-2025-002</TableCell>
                    <TableCell>Global Traders Ltd</TableCell>
                    <TableCell>Customs Clearance</TableCell>
                    <TableCell>$1,875.00</TableCell>
                    <TableCell>May 18, 2025</TableCell>
                    <TableCell>June 2, 2025</TableCell>
                    <TableCell>
                      <Badge variant="outline">Pending</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Send className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell className="font-medium">INV-2025-003</TableCell>
                    <TableCell>Tech Solutions Inc</TableCell>
                    <TableCell>Warehousing</TableCell>
                    <TableCell>$3,120.00</TableCell>
                    <TableCell>May 10, 2025</TableCell>
                    <TableCell>May 25, 2025</TableCell>
                    <TableCell>
                      <Badge variant="destructive">Overdue</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Send className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell className="font-medium">INV-2025-004</TableCell>
                    <TableCell>Maritime Logistics</TableCell>
                    <TableCell>Ocean Freight</TableCell>
                    <TableCell>$8,750.00</TableCell>
                    <TableCell>May 20, 2025</TableCell>
                    <TableCell>June 5, 2025</TableCell>
                    <TableCell>
                      <Badge variant="outline">Pending</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Send className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell className="font-medium">INV-2025-005</TableCell>
                    <TableCell>Express Delivery Co</TableCell>
                    <TableCell>Ground Transport</TableCell>
                    <TableCell>$2,450.00</TableCell>
                    <TableCell>May 22, 2025</TableCell>
                    <TableCell>June 6, 2025</TableCell>
                    <TableCell>
                      <Badge variant="default">Paid</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Pending Invoices Tab */}
        <TabsContent value="pending">
          <Card>
            <CardHeader>
              <CardTitle>Pending Invoices</CardTitle>
              <CardDescription>Invoices awaiting payment</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-4 border rounded-lg">
                  <div>
                    <div className="font-medium">INV-2025-002 - Global Traders Ltd</div>
                    <div className="text-sm text-muted-foreground">Customs Clearance Service</div>
                    <div className="text-sm text-muted-foreground">Due: June 2, 2025</div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold">$1,875.00</div>
                    <div className="flex gap-2 mt-2">
                      <Button size="sm" variant="outline">
                        <Send className="mr-1 h-3 w-3" />
                        Send Reminder
                      </Button>
                      <Button size="sm">
                        <Eye className="mr-1 h-3 w-3" />
                        View
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="flex justify-between items-center p-4 border rounded-lg">
                  <div>
                    <div className="font-medium">INV-2025-004 - Maritime Logistics</div>
                    <div className="text-sm text-muted-foreground">Ocean Freight Service</div>
                    <div className="text-sm text-muted-foreground">Due: June 5, 2025</div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold">$8,750.00</div>
                    <div className="flex gap-2 mt-2">
                      <Button size="sm" variant="outline">
                        <Send className="mr-1 h-3 w-3" />
                        Send Reminder
                      </Button>
                      <Button size="sm">
                        <Eye className="mr-1 h-3 w-3" />
                        View
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Overdue Invoices Tab */}
        <TabsContent value="overdue">
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">Overdue Invoices</CardTitle>
              <CardDescription>Invoices that require immediate attention</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-4 border border-red-200 rounded-lg bg-red-50">
                  <div>
                    <div className="font-medium">INV-2025-003 - Tech Solutions Inc</div>
                    <div className="text-sm text-muted-foreground">Warehousing Service</div>
                    <div className="text-sm text-red-600 font-medium">Overdue by 5 days</div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-red-600">$3,120.00</div>
                    <div className="flex gap-2 mt-2">
                      <Button size="sm" variant="destructive">
                        <Send className="mr-1 h-3 w-3" />
                        Final Notice
                      </Button>
                      <Button size="sm" variant="outline">
                        <Eye className="mr-1 h-3 w-3" />
                        View
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Paid Invoices Tab */}
        <TabsContent value="paid">
          <Card>
            <CardHeader>
              <CardTitle>Paid Invoices</CardTitle>
              <CardDescription>Successfully completed transactions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-4 border border-green-200 rounded-lg bg-green-50">
                  <div>
                    <div className="font-medium">INV-2025-001 - Acme Corporation</div>
                    <div className="text-sm text-muted-foreground">Air Freight Service</div>
                    <div className="text-sm text-green-600 font-medium">Paid on May 28, 2025</div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-green-600">$4,250.00</div>
                    <div className="flex gap-2 mt-2">
                      <Button size="sm" variant="outline">
                        <Download className="mr-1 h-3 w-3" />
                        Receipt
                      </Button>
                      <Button size="sm" variant="outline">
                        <Eye className="mr-1 h-3 w-3" />
                        View
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="flex justify-between items-center p-4 border border-green-200 rounded-lg bg-green-50">
                  <div>
                    <div className="font-medium">INV-2025-005 - Express Delivery Co</div>
                    <div className="text-sm text-muted-foreground">Ground Transport Service</div>
                    <div className="text-sm text-green-600 font-medium">Paid on May 30, 2025</div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-green-600">$2,450.00</div>
                    <div className="flex gap-2 mt-2">
                      <Button size="sm" variant="outline">
                        <Download className="mr-1 h-3 w-3" />
                        Receipt
                      </Button>
                      <Button size="sm" variant="outline">
                        <Eye className="mr-1 h-3 w-3" />
                        View
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-primary/10 rounded-md">
                <Plus className="h-6 w-6 text-primary" />
              </div>
              <div>
                <div className="font-medium">Create New Invoice</div>
                <div className="text-sm text-muted-foreground">Generate a new invoice</div>
              </div>
            </div>
            <Button className="w-full mt-4" asChild>
              <Link href="/admin/invoicing/create">Create Invoice</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-blue-100 rounded-md">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <div className="font-medium">Invoice Templates</div>
                <div className="text-sm text-muted-foreground">Manage templates</div>
              </div>
            </div>
            <Button variant="outline" className="w-full mt-4" asChild>
              <Link href="/admin/invoicing/templates">Manage Templates</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-green-100 rounded-md">
                <Download className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <div className="font-medium">Export Reports</div>
                <div className="text-sm text-muted-foreground">Download invoice reports</div>
              </div>
            </div>
            <Button variant="outline" className="w-full mt-4" asChild>
              <Link href="/admin/invoicing/reports">Export Reports</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-amber-100 rounded-md">
                <Clock className="h-6 w-6 text-amber-600" />
              </div>
              <div>
                <div className="font-medium">Payment Reminders</div>
                <div className="text-sm text-muted-foreground">Automated reminders</div>
              </div>
            </div>
            <Button variant="outline" className="w-full mt-4" asChild>
              <Link href="/admin/invoicing/reminders">Setup Reminders</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
