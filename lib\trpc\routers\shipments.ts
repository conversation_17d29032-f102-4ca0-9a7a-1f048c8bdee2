import { z } from 'zod'
import { createTRPCRouter, protectedProcedure, staffProcedure } from '../server'
import { TRPCError } from '@trpc/server'
import { ShipmentStatus, UserRole } from '@prisma/client'

const createShipmentSchema = z.object({
  serviceId: z.string(),
  origin: z.string().min(1, 'Origin is required'),
  destination: z.string().min(1, 'Destination is required'),
  weight: z.number().positive().optional(),
  dimensions: z.object({
    length: z.number().positive(),
    width: z.number().positive(),
    height: z.number().positive(),
  }).optional(),
  value: z.number().positive().optional(),
  currency: z.string().default('USD'),
  notes: z.string().optional(),
})

const updateShipmentSchema = z.object({
  id: z.string(),
  status: z.nativeEnum(ShipmentStatus).optional(),
  estimatedDelivery: z.date().optional(),
  actualDelivery: z.date().optional(),
  notes: z.string().optional(),
})

const trackingSchema = z.object({
  trackingNumber: z.string(),
})

export const shipmentsRouter = createTRPCRouter({
  create: protectedProcedure
    .input(createShipmentSchema)
    .mutation(async ({ ctx, input }) => {
      // Get customer ID
      let customerId: string

      if (ctx.session.user.role === UserRole.CUSTOMER) {
        const customer = await ctx.db.customer.findUnique({
          where: { userId: ctx.session.user.id }
        })
        if (!customer) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Customer profile not found'
          })
        }
        customerId = customer.id
      } else {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Only customers can create shipments'
        })
      }

      // Verify service exists
      const service = await ctx.db.service.findUnique({
        where: { id: input.serviceId }
      })

      if (!service || !service.isActive) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Service not found or inactive'
        })
      }

      // Generate tracking number
      const trackingNumber = `DCF${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`

      const shipment = await ctx.db.shipment.create({
        data: {
          trackingNumber,
          customerId,
          serviceId: input.serviceId,
          origin: input.origin,
          destination: input.destination,
          weight: input.weight,
          dimensions: input.dimensions,
          value: input.value,
          currency: input.currency,
          notes: input.notes,
          status: ShipmentStatus.PENDING,
        },
        include: {
          service: true,
          customer: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true,
                }
              }
            }
          }
        }
      })

      return shipment
    }),

  getAll: protectedProcedure
    .input(z.object({
      page: z.number().default(1),
      limit: z.number().default(10),
      status: z.nativeEnum(ShipmentStatus).optional(),
      search: z.string().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { page, limit, status, search } = input
      const skip = (page - 1) * limit

      let where: any = {}

      // Filter by customer if user is a customer
      if (ctx.session.user.role === UserRole.CUSTOMER) {
        const customer = await ctx.db.customer.findUnique({
          where: { userId: ctx.session.user.id }
        })
        if (customer) {
          where.customerId = customer.id
        }
      }

      // Add status filter
      if (status) {
        where.status = status
      }

      // Add search filter
      if (search) {
        where.OR = [
          { trackingNumber: { contains: search, mode: 'insensitive' } },
          { origin: { contains: search, mode: 'insensitive' } },
          { destination: { contains: search, mode: 'insensitive' } },
        ]
      }

      const [shipments, total] = await Promise.all([
        ctx.db.shipment.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            service: true,
            customer: {
              include: {
                user: {
                  select: {
                    name: true,
                    email: true,
                  }
                }
              }
            }
          }
        }),
        ctx.db.shipment.count({ where })
      ])

      return {
        shipments,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      let where: any = { id: input.id }

      // Filter by customer if user is a customer
      if (ctx.session.user.role === UserRole.CUSTOMER) {
        const customer = await ctx.db.customer.findUnique({
          where: { userId: ctx.session.user.id }
        })
        if (customer) {
          where.customerId = customer.id
        }
      }

      const shipment = await ctx.db.shipment.findFirst({
        where,
        include: {
          service: true,
          customer: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true,
                }
              }
            }
          },
          documents: true,
          invoice: true,
        }
      })

      if (!shipment) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Shipment not found'
        })
      }

      return shipment
    }),

  track: protectedProcedure
    .input(trackingSchema)
    .query(async ({ ctx, input }) => {
      const shipment = await ctx.db.shipment.findUnique({
        where: { trackingNumber: input.trackingNumber },
        include: {
          service: true,
          customer: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true,
                }
              }
            }
          }
        }
      })

      if (!shipment) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Shipment not found'
        })
      }

      // Check if user has access to this shipment
      if (ctx.session.user.role === UserRole.CUSTOMER) {
        const customer = await ctx.db.customer.findUnique({
          where: { userId: ctx.session.user.id }
        })
        if (!customer || shipment.customerId !== customer.id) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Access denied'
          })
        }
      }

      return shipment
    }),

  update: staffProcedure
    .input(updateShipmentSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input

      const shipment = await ctx.db.shipment.update({
        where: { id },
        data: updateData,
        include: {
          service: true,
          customer: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true,
                }
              }
            }
          }
        }
      })

      return shipment
    }),

  delete: staffProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db.shipment.delete({
        where: { id: input.id }
      })

      return { success: true }
    }),

  getStats: staffProcedure
    .query(async ({ ctx }) => {
      const [
        totalShipments,
        pendingShipments,
        inTransitShipments,
        deliveredShipments,
        recentShipments
      ] = await Promise.all([
        ctx.db.shipment.count(),
        ctx.db.shipment.count({ where: { status: ShipmentStatus.PENDING } }),
        ctx.db.shipment.count({ where: { status: ShipmentStatus.IN_TRANSIT } }),
        ctx.db.shipment.count({ where: { status: ShipmentStatus.DELIVERED } }),
        ctx.db.shipment.findMany({
          take: 5,
          orderBy: { createdAt: 'desc' },
          include: {
            service: true,
            customer: {
              include: {
                user: {
                  select: {
                    name: true,
                    email: true,
                  }
                }
              }
            }
          }
        })
      ])

      return {
        totalShipments,
        pendingShipments,
        inTransitShipments,
        deliveredShipments,
        recentShipments
      }
    }),
})
