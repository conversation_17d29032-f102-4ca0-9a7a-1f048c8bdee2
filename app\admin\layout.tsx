import type { ReactNode } from "react"
import type { Metada<PERSON> } from "next"
import AdminSidebar from "@/components/admin/admin-sidebar"
import AdminHeader from "@/components/admin/admin-header"
import AdminFooter from "@/components/admin/admin-footer"
import { redirect } from "next/navigation"

export const metadata: Metadata = {
  title: "Admin Dashboard | DCF Logistics",
  description: "Administrative dashboard for DCF Logistics management",
}

// This would typically check authentication and role
async function checkAdminAccess() {
  // In a real app, you would check:
  // 1. If user is authenticated
  // 2. If user has admin/staff/CEO role
  // 3. Redirect to login if not authenticated
  // 4. Redirect to unauthorized if not admin
  return true
}

export default async function AdminLayout({ children }: { children: ReactNode }) {
  const hasAccess = await checkAdminAccess()

  if (!hasAccess) {
    redirect("/account/login")
  }

  return (
    <div className="flex h-screen bg-gray-50/50 overflow-hidden">
      <AdminSidebar />
      <div className="flex flex-1 flex-col">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">{children}</main>
        <AdminFooter />
      </div>
    </div>
  )
}
