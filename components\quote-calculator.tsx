"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Calculator } from "lucide-react"
import { Button } from "@/components/ui/button"

interface CalculatedQuote {
  baseRate: number
  distanceFee: number
  weightFee: number
  serviceFees: {
    name: string
    fee: number
  }[]
  totalCost: number
  estimatedTime: string
}

export function QuoteCalculator() {
  const [formData, setFormData] = useState({
    serviceType: "",
    origin: "",
    destination: "",
    weight: "",
    length: "",
    width: "",
    height: "",
    cargoType: "general",
    additionalServices: [] as string[],
  })

  const [calculatedQuote, setCalculatedQuote] = useState<CalculatedQuote | null>(null)
  const [isCalculating, setIsCalculating] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleCheckboxChange = (value: string, checked: boolean) => {
    setFormData((prev) => {
      if (checked) {
        return { ...prev, additionalServices: [...prev.additionalServices, value] }
      } else {
        return {
          ...prev,
          additionalServices: prev.additionalServices.filter((service) => service !== value),
        }
      }
    })
  }

  const calculateQuote = () => {
    setIsCalculating(true)

    // This is a simplified mock calculation
    // In a real application, this would call an API with complex pricing logic
    setTimeout(() => {
      // Mock distance calculation based on origin/destination
      const distanceMap: Record<string, Record<string, number>> = {
        banjul: {
          accra: 1800,
          lagos: 2500,
          dakar: 300,
        },
        accra: {
          banjul: 1800,
          lagos: 700,
          dakar: 2100,
        },
        lagos: {
          banjul: 2500,
          accra: 700,
          dakar: 2800,
        },
        dakar: {
          banjul: 300,
          accra: 2100,
          lagos: 2800,
        },
      }

      // Service type base rates
      const baseRates: Record<string, number> = {
        "air-freight": 8.5,
        "ocean-freight": 2.5,
        "road-transport": 1.8,
        "express-delivery": 12.0,
      }

      // Additional service fees
      const serviceFees: Record<string, number> = {
        insurance: 50,
        customs: 75,
        packaging: 40,
        warehousing: 60,
        "door-to-door": 80,
        tracking: 25,
      }

      // Calculate distance fee
      const distance = distanceMap[formData.origin.toLowerCase()]?.[formData.destination.toLowerCase()] || 1000
      const distanceFee = distance * 0.05

      // Calculate weight and volume
      const weight = Number.parseFloat(formData.weight) || 10
      const length = Number.parseFloat(formData.length) || 0
      const width = Number.parseFloat(formData.width) || 0
      const height = Number.parseFloat(formData.height) || 0
      const volumetricWeight = length && width && height ? (length * width * height) / 5000 : 0
      const chargeableWeight = Math.max(weight, volumetricWeight)
      const weightFee = chargeableWeight * (baseRates[formData.serviceType] || 5)

      // Calculate cargo type multiplier
      const cargoMultipliers: Record<string, number> = {
        general: 1,
        hazardous: 1.5,
        perishable: 1.3,
        oversized: 1.4,
        valuable: 1.6,
        other: 1.2,
      }
      const cargoMultiplier = cargoMultipliers[formData.cargoType] || 1

      // Calculate base rate
      const baseRate = (baseRates[formData.serviceType] || 5) * 100 * cargoMultiplier

      // Calculate additional service fees
      const additionalServiceFees = formData.additionalServices.map((service) => ({
        name: service,
        fee: serviceFees[service] || 0,
      }))

      // Calculate total
      const totalServiceFees = additionalServiceFees.reduce((sum, service) => sum + service.fee, 0)
      const totalCost = baseRate + distanceFee + weightFee + totalServiceFees

      // Estimate delivery time
      let estimatedTime = "7-10 business days"
      if (formData.serviceType === "air-freight") {
        estimatedTime = "2-3 business days"
      } else if (formData.serviceType === "express-delivery") {
        estimatedTime = "1-2 business days"
      } else if (formData.serviceType === "ocean-freight") {
        estimatedTime = "14-21 business days"
      }

      setCalculatedQuote({
        baseRate,
        distanceFee,
        weightFee,
        serviceFees: additionalServiceFees,
        totalCost,
        estimatedTime,
      })

      setIsCalculating(false)
    }, 1500)
  }

  const handleCalculate = (e: React.FormEvent) => {
    e.preventDefault()
    calculateQuote()
  }

  const handleReset = () => {
    setFormData({
      serviceType: "",
      origin: "",
      destination: "",
      weight: "",
      length: "",
      width: "",
      height: "",
      cargoType: "general",
      additionalServices: [],
    })
    setCalculatedQuote(null)
  }

  return (
    <div className="space-y-8">
      <form onSubmit={handleCalculate} className="space-y-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="serviceType">Service Type *</Label>
            <Select
              value={formData.serviceType}
              onValueChange={(value) => handleSelectChange("serviceType", value)}
              required
            >
              <SelectTrigger id="serviceType">
                <SelectValue placeholder="Select service type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="air-freight">Air Freight</SelectItem>
                <SelectItem value="ocean-freight">Ocean Freight</SelectItem>
                <SelectItem value="road-transport">Road Transportation</SelectItem>
                <SelectItem value="express-delivery">Express Delivery</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="origin">Origin *</Label>
              <Select value={formData.origin} onValueChange={(value) => handleSelectChange("origin", value)} required>
                <SelectTrigger id="origin">
                  <SelectValue placeholder="Select origin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="banjul">Banjul, Gambia</SelectItem>
                  <SelectItem value="accra">Accra, Ghana</SelectItem>
                  <SelectItem value="lagos">Lagos, Nigeria</SelectItem>
                  <SelectItem value="dakar">Dakar, Senegal</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="destination">Destination *</Label>
              <Select
                value={formData.destination}
                onValueChange={(value) => handleSelectChange("destination", value)}
                required
              >
                <SelectTrigger id="destination">
                  <SelectValue placeholder="Select destination" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="banjul">Banjul, Gambia</SelectItem>
                  <SelectItem value="accra">Accra, Ghana</SelectItem>
                  <SelectItem value="lagos">Lagos, Nigeria</SelectItem>
                  <SelectItem value="dakar">Dakar, Senegal</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="weight">Weight (kg) *</Label>
            <Input
              id="weight"
              name="weight"
              type="number"
              min="0"
              step="0.1"
              value={formData.weight}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="space-y-2">
            <Label>Dimensions (cm)</Label>
            <div className="grid grid-cols-3 gap-2">
              <div>
                <Input
                  id="length"
                  name="length"
                  type="number"
                  min="0"
                  step="0.1"
                  placeholder="Length"
                  value={formData.length}
                  onChange={handleInputChange}
                />
              </div>
              <div>
                <Input
                  id="width"
                  name="width"
                  type="number"
                  min="0"
                  step="0.1"
                  placeholder="Width"
                  value={formData.width}
                  onChange={handleInputChange}
                />
              </div>
              <div>
                <Input
                  id="height"
                  name="height"
                  type="number"
                  min="0"
                  step="0.1"
                  placeholder="Height"
                  value={formData.height}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Cargo Type *</Label>
            <RadioGroup
              value={formData.cargoType}
              onValueChange={(value) => handleSelectChange("cargoType", value)}
              required
            >
              <div className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="general" id="general-cargo" />
                  <Label htmlFor="general-cargo">General Cargo</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="hazardous" id="hazardous-cargo" />
                  <Label htmlFor="hazardous-cargo">Hazardous</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="perishable" id="perishable-cargo" />
                  <Label htmlFor="perishable-cargo">Perishable</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="oversized" id="oversized-cargo" />
                  <Label htmlFor="oversized-cargo">Oversized</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="valuable" id="valuable-cargo" />
                  <Label htmlFor="valuable-cargo">Valuable</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="other" id="other-cargo-type" />
                  <Label htmlFor="other-cargo-type">Other</Label>
                </div>
              </div>
            </RadioGroup>
          </div>

          <div className="space-y-2">
            <Label>Additional Services</Label>
            <div className="grid grid-cols-2 gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="insurance-service"
                  checked={formData.additionalServices.includes("insurance")}
                  onCheckedChange={(checked) => handleCheckboxChange("insurance", checked as boolean)}
                />
                <Label htmlFor="insurance-service">Cargo Insurance</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="customs-service"
                  checked={formData.additionalServices.includes("customs")}
                  onCheckedChange={(checked) => handleCheckboxChange("customs", checked as boolean)}
                />
                <Label htmlFor="customs-service">Customs Clearance</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="packaging-service"
                  checked={formData.additionalServices.includes("packaging")}
                  onCheckedChange={(checked) => handleCheckboxChange("packaging", checked as boolean)}
                />
                <Label htmlFor="packaging-service">Packaging</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="warehousing-service"
                  checked={formData.additionalServices.includes("warehousing")}
                  onCheckedChange={(checked) => handleCheckboxChange("warehousing", checked as boolean)}
                />
                <Label htmlFor="warehousing-service">Warehousing</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="door-to-door-service"
                  checked={formData.additionalServices.includes("door-to-door")}
                  onCheckedChange={(checked) => handleCheckboxChange("door-to-door", checked as boolean)}
                />
                <Label htmlFor="door-to-door-service">Door-to-Door</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="tracking-service"
                  checked={formData.additionalServices.includes("tracking")}
                  onCheckedChange={(checked) => handleCheckboxChange("tracking", checked as boolean)}
                />
                <Label htmlFor="tracking-service">Advanced Tracking</Label>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-3">
          <Button type="submit" className="flex-1" disabled={isCalculating}>
            {isCalculating ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Calculating...
              </>
            ) : (
              <>
                <Calculator className="mr-2 h-4 w-4" /> Calculate Shipping Cost
              </>
            )}
          </Button>
          <Button type="button" variant="outline" onClick={handleReset} disabled={isCalculating}>
            Reset
          </Button>
        </div>
      </form>

      {calculatedQuote && (
        <Card>
          <CardHeader>
            <CardTitle>Shipping Cost Estimate</CardTitle>
            <CardDescription>Based on the information provided</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">Base Rate</h4>
                  <p className="text-lg font-semibold">${calculatedQuote.baseRate.toFixed(2)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">Distance Fee</h4>
                  <p className="text-lg font-semibold">${calculatedQuote.distanceFee.toFixed(2)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">Weight Fee</h4>
                  <p className="text-lg font-semibold">${calculatedQuote.weightFee.toFixed(2)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">Estimated Delivery</h4>
                  <p className="text-lg font-semibold">{calculatedQuote.estimatedTime}</p>
                </div>
              </div>

              {calculatedQuote.serviceFees.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-2">Additional Services</h4>
                  <div className="space-y-1">
                    {calculatedQuote.serviceFees.map((service, index) => (
                      <div key={index} className="flex justify-between">
                        <span className="text-sm">{service.name}</span>
                        <span className="text-sm font-medium">${service.fee.toFixed(2)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="pt-4 border-t">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">Total Estimated Cost</h3>
                  <p className="text-2xl font-bold text-primary">${calculatedQuote.totalCost.toFixed(2)}</p>
                </div>
              </div>

              <div className="bg-muted p-3 rounded-md text-sm text-muted-foreground">
                <p>
                  This is an estimate based on the information provided. Actual shipping costs may vary based on
                  additional factors. For a detailed quote, please contact our team.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
