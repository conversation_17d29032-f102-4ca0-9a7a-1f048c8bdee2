"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  Package, 
  MapPin, 
  User, 
  FileText,
  Calendar,
  Weight,
  DollarSign,
  Save,
  Send
} from 'lucide-react'
import Link from 'next/link'
import { toast } from 'sonner'

interface PackageFormData {
  invoiceNumber: string
  customerName: string
  customerEmail: string
  customerPhone: string
  origin: string
  destination: string
  weight: string
  dimensions: {
    length: string
    width: string
    height: string
  }
  value: string
  currency: string
  description: string
  specialInstructions: string
  estimatedDelivery: string
  serviceType: string
}

export default function RegisterPackagePage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [trackingNumber, setTrackingNumber] = useState('')
  const [availableInvoices, setAvailableInvoices] = useState<any[]>([])
  const [formData, setFormData] = useState<PackageFormData>({
    invoiceNumber: '',
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    origin: 'Banjul, Gambia',
    destination: '',
    weight: '',
    dimensions: {
      length: '',
      width: '',
      height: ''
    },
    value: '',
    currency: 'USD',
    description: '',
    specialInstructions: '',
    estimatedDelivery: '',
    serviceType: ''
  })

  useEffect(() => {
    // Generate tracking number
    const generateTrackingNumber = () => {
      const timestamp = Date.now().toString().slice(-6)
      const random = Math.random().toString(36).substring(2, 5).toUpperCase()
      return `DCF-TRK-${timestamp}${random}`
    }
    
    setTrackingNumber(generateTrackingNumber())
    
    // Fetch available invoices
    fetchAvailableInvoices()
  }, [])

  const fetchAvailableInvoices = async () => {
    try {
      // In production, this would fetch from the API
      // const response = await fetch('/api/admin/invoices?status=SENT,PAID&hasPackage=false')
      // const data = await response.json()
      
      // Mock data for demonstration
      const mockInvoices = [
        {
          id: '1',
          invoiceNumber: 'INV-2024-001',
          customerName: 'ABC Trading Ltd',
          customerEmail: '<EMAIL>',
          amount: 2450,
          serviceType: 'Air Freight'
        },
        {
          id: '2',
          invoiceNumber: 'INV-2024-002',
          customerName: 'XYZ Logistics',
          customerEmail: '<EMAIL>',
          amount: 1850,
          serviceType: 'Ground Transport'
        }
      ]
      
      setAvailableInvoices(mockInvoices)
    } catch (error) {
      console.error('Failed to fetch invoices:', error)
    }
  }

  const handleInvoiceSelect = (invoiceNumber: string) => {
    const invoice = availableInvoices.find(inv => inv.invoiceNumber === invoiceNumber)
    if (invoice) {
      setFormData(prev => ({
        ...prev,
        invoiceNumber,
        customerName: invoice.customerName,
        customerEmail: invoice.customerEmail,
        serviceType: invoice.serviceType
      }))
    }
  }

  const handleSubmit = async (action: 'register' | 'register_and_start') => {
    try {
      setLoading(true)

      // Validate required fields
      if (!formData.customerName || !formData.destination || !formData.description) {
        toast.error('Please fill in all required fields')
        return
      }

      const payload = {
        trackingNumber,
        ...formData,
        status: action === 'register_and_start' ? 'IN_TRANSIT' : 'REGISTERED'
      }

      // In production, this would be an API call
      // const response = await fetch('/api/admin/packages', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(payload)
      // })

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast.success(
        action === 'register_and_start'
          ? `Package ${trackingNumber} registered and shipment started`
          : `Package ${trackingNumber} registered successfully`
      )

      router.push('/admin/packages')
    } catch (error) {
      console.error('Failed to register package:', error)
      toast.error('Failed to register package. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/packages">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Packages
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Register New Package</h1>
            <p className="text-gray-600">Create a new package for tracking</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => handleSubmit('register')}
            disabled={loading}
          >
            <Save className="h-4 w-4 mr-2" />
            Register Only
          </Button>
          <Button 
            onClick={() => handleSubmit('register_and_start')}
            disabled={loading}
          >
            <Send className="h-4 w-4 mr-2" />
            Register & Start Shipment
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Package Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Tracking Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Tracking Information
              </CardTitle>
              <CardDescription>Package identification and tracking details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="trackingNumber">Tracking Number</Label>
                  <Input
                    id="trackingNumber"
                    value={trackingNumber}
                    readOnly
                    className="font-mono bg-gray-50"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="invoiceNumber">Link to Invoice (Optional)</Label>
                  <Select 
                    value={formData.invoiceNumber} 
                    onValueChange={handleInvoiceSelect}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select invoice to link" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableInvoices.map((invoice) => (
                        <SelectItem key={invoice.id} value={invoice.invoiceNumber}>
                          {invoice.invoiceNumber} - {invoice.customerName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Customer Information
              </CardTitle>
              <CardDescription>Customer details for the package</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="customerName">Customer Name *</Label>
                  <Input
                    id="customerName"
                    value={formData.customerName}
                    onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
                    placeholder="Enter customer name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="customerEmail">Email Address</Label>
                  <Input
                    id="customerEmail"
                    type="email"
                    value={formData.customerEmail}
                    onChange={(e) => setFormData(prev => ({ ...prev, customerEmail: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="customerPhone">Phone Number</Label>
                  <Input
                    id="customerPhone"
                    value={formData.customerPhone}
                    onChange={(e) => setFormData(prev => ({ ...prev, customerPhone: e.target.value }))}
                    placeholder="+************"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="serviceType">Service Type</Label>
                  <Select 
                    value={formData.serviceType} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, serviceType: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select service type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="air-freight">Air Freight</SelectItem>
                      <SelectItem value="sea-freight">Sea Freight</SelectItem>
                      <SelectItem value="ground-transport">Ground Transport</SelectItem>
                      <SelectItem value="express-delivery">Express Delivery</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Shipment Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Shipment Details
              </CardTitle>
              <CardDescription>Origin, destination, and package specifications</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="origin">Origin</Label>
                  <Input
                    id="origin"
                    value={formData.origin}
                    onChange={(e) => setFormData(prev => ({ ...prev, origin: e.target.value }))}
                    placeholder="Origin location"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="destination">Destination *</Label>
                  <Input
                    id="destination"
                    value={formData.destination}
                    onChange={(e) => setFormData(prev => ({ ...prev, destination: e.target.value }))}
                    placeholder="Destination location"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="weight">Weight (kg)</Label>
                  <Input
                    id="weight"
                    type="number"
                    min="0"
                    step="0.1"
                    value={formData.weight}
                    onChange={(e) => setFormData(prev => ({ ...prev, weight: e.target.value }))}
                    placeholder="0.0"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="estimatedDelivery">Estimated Delivery</Label>
                  <Input
                    id="estimatedDelivery"
                    type="date"
                    value={formData.estimatedDelivery}
                    onChange={(e) => setFormData(prev => ({ ...prev, estimatedDelivery: e.target.value }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Dimensions (cm)</Label>
                <div className="grid grid-cols-3 gap-2">
                  <Input
                    placeholder="Length"
                    value={formData.dimensions.length}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      dimensions: { ...prev.dimensions, length: e.target.value }
                    }))}
                  />
                  <Input
                    placeholder="Width"
                    value={formData.dimensions.width}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      dimensions: { ...prev.dimensions, width: e.target.value }
                    }))}
                  />
                  <Input
                    placeholder="Height"
                    value={formData.dimensions.height}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      dimensions: { ...prev.dimensions, height: e.target.value }
                    }))}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="value">Declared Value</Label>
                  <Input
                    id="value"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.value}
                    onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                    placeholder="0.00"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="currency">Currency</Label>
                  <Select 
                    value={formData.currency} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, currency: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD ($)</SelectItem>
                      <SelectItem value="EUR">EUR (€)</SelectItem>
                      <SelectItem value="GBP">GBP (£)</SelectItem>
                      <SelectItem value="GMD">GMD (D)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Package Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe the package contents..."
                  rows={3}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="specialInstructions">Special Instructions</Label>
                <Textarea
                  id="specialInstructions"
                  value={formData.specialInstructions}
                  onChange={(e) => setFormData(prev => ({ ...prev, specialInstructions: e.target.value }))}
                  placeholder="Any special handling instructions..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Package Summary */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Package Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium">Tracking Number</p>
                  <p className="font-mono text-sm bg-gray-50 p-2 rounded">{trackingNumber}</p>
                </div>
                
                <div>
                  <p className="text-sm font-medium">Customer</p>
                  <p className="text-sm">{formData.customerName || 'Not specified'}</p>
                </div>
                
                <div>
                  <p className="text-sm font-medium">Route</p>
                  <p className="text-sm">{formData.origin} → {formData.destination || 'Not specified'}</p>
                </div>
                
                {formData.weight && (
                  <div>
                    <p className="text-sm font-medium">Weight</p>
                    <p className="text-sm">{formData.weight} kg</p>
                  </div>
                )}
                
                {formData.value && (
                  <div>
                    <p className="text-sm font-medium">Declared Value</p>
                    <p className="text-sm">{formData.value} {formData.currency}</p>
                  </div>
                )}
                
                {formData.estimatedDelivery && (
                  <div>
                    <p className="text-sm font-medium">Estimated Delivery</p>
                    <p className="text-sm">{new Date(formData.estimatedDelivery).toLocaleDateString()}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Registration Options</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <Badge variant="outline">Register Only</Badge>
                <span>Create package record for future shipment</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Badge variant="default">Register & Start</Badge>
                <span>Create package and mark as in transit</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
