import type { Metada<PERSON> } from "next"
import { QuoteCalculator } from "@/components/quote-calculator"
import Link from "next/link"
import { Button } from "@/components/ui/button"

export const metadata: Metadata = {
  title: "Shipping Cost Calculator | Global Logistics Solutions",
  description:
    "Calculate shipping costs instantly with our online calculator. Get estimates for different shipping methods and destinations.",
}

export default function CalculatorPage() {
  return (
    <div className="container mx-auto py-12 px-4 md:px-6">
      <div className="max-w-5xl mx-auto">
        <div className="mb-10 text-center">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Shipping Cost Calculator</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Get instant shipping cost estimates based on your shipment details. This calculator provides approximate
            costs for planning purposes.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-12">
          <div className="bg-primary/5 rounded-lg p-6 flex flex-col items-center text-center">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary"
              >
                <path d="M3 3v18h18"></path>
                <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"></path>
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">Instant Estimates</h3>
            <p className="text-muted-foreground">Get immediate cost estimates for your shipments</p>
          </div>

          <div className="bg-primary/5 rounded-lg p-6 flex flex-col items-center text-center">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary"
              >
                <path d="M12 2H2v10h10V2z"></path>
                <path d="M22 12h-10v10h10V12z"></path>
                <path d="M12 12H2v10h10V12z"></path>
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">Compare Options</h3>
            <p className="text-muted-foreground">Compare different shipping methods and routes</p>
          </div>

          <div className="bg-primary/5 rounded-lg p-6 flex flex-col items-center text-center">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary"
              >
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path>
                <path d="M12 8v4"></path>
                <path d="M12 16h.01"></path>
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">Plan Ahead</h3>
            <p className="text-muted-foreground">Make informed decisions for your logistics planning</p>
          </div>
        </div>

        <div className="bg-card shadow-lg rounded-lg border">
          <div className="p-6 md:p-8">
            <QuoteCalculator />
          </div>
        </div>

        <div className="mt-10 text-center">
          <p className="mb-4 text-muted-foreground">Need a more detailed quote or have special requirements?</p>
          <Button asChild variant="outline" className="mr-4">
            <Link href="/quote">Request Custom Quote</Link>
          </Button>
          <Button asChild>
            <Link href="/services/compare">Compare Services</Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
