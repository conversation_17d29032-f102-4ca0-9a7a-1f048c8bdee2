"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Plus,
  Search,
  Filter,
  MapPin,
  Package,
  Truck,
  Plane,
  Eye,
  Edit,
  Clock,
  CheckCircle,
  AlertTriangle,
  RotateCcw,
  Loader2
} from 'lucide-react'
import Link from 'next/link'
import { trpc } from '@/lib/trpc/client'
import { ShipmentStatus } from '@prisma/client'
import { toast } from 'sonner'

export default function PackagesPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<ShipmentStatus | 'ALL'>('ALL')
  const [currentPage, setCurrentPage] = useState(1)

  // Fetch shipments (packages) with filters
  const { data: shipmentsData, isLoading, refetch } = trpc.shipments.getAll.useQuery({
    page: currentPage,
    limit: 10,
    search: searchTerm || undefined,
    status: statusFilter !== 'ALL' ? statusFilter : undefined,
  })

  // Fetch shipment statistics
  const { data: stats, isLoading: statsLoading } = trpc.shipments.getStats.useQuery()

  // Update shipment status mutation
  const updateShipmentMutation = trpc.shipments.updateStatus.useMutation({
    onSuccess: () => {
      toast.success("Package status updated successfully")
      refetch()
    },
    onError: (error) => {
      toast.error(`Failed to update package status: ${error.message}`)
    }
  })

  const handleStatusUpdate = async (id: string, status: ShipmentStatus) => {
    try {
      await updateShipmentMutation.mutateAsync({ id, status })
    } catch (error) {
      console.error('Failed to update shipment status:', error)
    }
  }
        {
          id: '1',
          trackingNumber: 'DCF-TRK-789123',
          invoiceNumber: 'INV-2024-001',
          customerName: 'ABC Trading Ltd',
          origin: 'Banjul, Gambia',
          destination: 'Lagos, Nigeria',
          status: 'DELIVERED',
          weight: 25.5,
          value: 2450,
          estimatedDelivery: '2024-12-20',
          actualDelivery: '2024-12-19',
          createdAt: '2024-12-15',
          lastUpdate: '2024-12-19'
        },
        {
          id: '2',
          trackingNumber: 'DCF-TRK-456789',
          invoiceNumber: 'INV-2024-002',
          customerName: 'XYZ Logistics',
          origin: 'Banjul, Gambia',
          destination: 'Dakar, Senegal',
          status: 'IN_TRANSIT',
          weight: 15.2,
          value: 1850,
          estimatedDelivery: '2024-12-22',
          createdAt: '2024-12-18',
          lastUpdate: '2024-12-20'
        },
        {
          id: '3',
          trackingNumber: 'DCF-TRK-123456',
          customerName: 'Global Imports Co',
          origin: 'Banjul, Gambia',
          destination: 'Bamako, Mali',
          status: 'OUT_FOR_DELIVERY',
          weight: 42.8,
          value: 3200,
          estimatedDelivery: '2024-12-21',
          createdAt: '2024-12-16',
          lastUpdate: '2024-12-21'
        },
        {
          id: '4',
          trackingNumber: 'DCF-TRK-987654',
          invoiceNumber: 'INV-2024-004',
          customerName: 'Tech Solutions Inc',
          origin: 'Banjul, Gambia',
          destination: 'Accra, Ghana',
          status: 'REGISTERED',
          weight: 8.5,
          value: 1200,
          estimatedDelivery: '2024-12-25',
          createdAt: '2024-12-19',
          lastUpdate: '2024-12-19'
        }
      ]

      setPackages(mockPackages)

      // Calculate stats
      setStats({
        total: mockPackages.length,
        registered: mockPackages.filter(pkg => pkg.status === 'REGISTERED').length,
        inTransit: mockPackages.filter(pkg => pkg.status === 'IN_TRANSIT' || pkg.status === 'OUT_FOR_DELIVERY').length,
        delivered: mockPackages.filter(pkg => pkg.status === 'DELIVERED').length,
        delayed: mockPackages.filter(pkg => {
          if (!pkg.estimatedDelivery) return false
          const estimated = new Date(pkg.estimatedDelivery)
          const now = new Date()
          return estimated < now && pkg.status !== 'DELIVERED'
        }).length
      })
    } catch (error) {
      console.error('Failed to fetch packages:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredPackages = packages.filter(pkg => {
    const matchesSearch = pkg.trackingNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pkg.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (pkg.invoiceNumber && pkg.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesStatus = statusFilter === 'all' || pkg.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DELIVERED': return 'bg-green-100 text-green-800'
      case 'IN_TRANSIT': return 'bg-blue-100 text-blue-800'
      case 'OUT_FOR_DELIVERY': return 'bg-purple-100 text-purple-800'
      case 'REGISTERED': return 'bg-gray-100 text-gray-800'
      case 'RETURNED': return 'bg-yellow-100 text-yellow-800'
      case 'LOST': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'DELIVERED': return <CheckCircle className="h-4 w-4" />
      case 'IN_TRANSIT': return <Truck className="h-4 w-4" />
      case 'OUT_FOR_DELIVERY': return <MapPin className="h-4 w-4" />
      case 'REGISTERED': return <Package className="h-4 w-4" />
      case 'RETURNED': return <RotateCcw className="h-4 w-4" />
      case 'LOST': return <AlertTriangle className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Package Tracking</h1>
          <p className="text-gray-600">Monitor and manage all package shipments</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Advanced Filter
          </Button>
          <Button asChild>
            <Link href="/admin/packages/register">
              <Plus className="h-4 w-4 mr-2" />
              Register Package
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Packages</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Registered</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{stats.registered}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Transit</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.inTransit}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivered</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.delivered}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delayed</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.delayed}</div>
          </CardContent>
        </Card>
      </div>

      {/* Package List */}
      <Card>
        <CardHeader>
          <CardTitle>All Packages</CardTitle>
          <CardDescription>Track and manage package shipments</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by tracking number, customer, or invoice..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="REGISTERED">Registered</SelectItem>
                <SelectItem value="IN_TRANSIT">In Transit</SelectItem>
                <SelectItem value="OUT_FOR_DELIVERY">Out for Delivery</SelectItem>
                <SelectItem value="DELIVERED">Delivered</SelectItem>
                <SelectItem value="RETURNED">Returned</SelectItem>
                <SelectItem value="LOST">Lost</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Package Table */}
          {loading ? (
            <div className="text-center py-8">Loading packages...</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tracking Number</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Route</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Weight</TableHead>
                  <TableHead>Estimated Delivery</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPackages.map((pkg) => (
                  <TableRow key={pkg.id}>
                    <TableCell>
                      <div>
                        <div className="font-mono font-medium">{pkg.trackingNumber}</div>
                        {pkg.invoiceNumber && (
                          <div className="text-sm text-gray-500">Invoice: {pkg.invoiceNumber}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{pkg.customerName}</div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          {pkg.origin}
                        </div>
                        <div className="text-gray-500 mt-1">→ {pkg.destination}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={`flex items-center gap-1 w-fit ${getStatusColor(pkg.status)}`}>
                        {getStatusIcon(pkg.status)}
                        {pkg.status.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {pkg.weight ? `${pkg.weight} kg` : '-'}
                    </TableCell>
                    <TableCell>
                      {pkg.estimatedDelivery ? new Date(pkg.estimatedDelivery).toLocaleDateString() : '-'}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/admin/packages/${pkg.id}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/admin/packages/${pkg.id}/edit`}>
                            <Edit className="h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}

          {filteredPackages.length === 0 && !loading && (
            <div className="text-center py-8 text-gray-500">
              No packages found matching your criteria
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
