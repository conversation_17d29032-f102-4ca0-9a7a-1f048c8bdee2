"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Mail, Users, TrendingUp, Download, Search, Filter } from 'lucide-react'
import { toast } from 'sonner'

interface NewsletterSubscriber {
  id: string
  email: string
  name?: string
  isActive: boolean
  source?: string
  subscribedAt: string
  unsubscribedAt?: string
}

export default function NewsletterManagementPage() {
  const [subscribers, setSubscribers] = useState<NewsletterSubscriber[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [sourceFilter, setSourceFilter] = useState('all')
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    thisMonth: 0
  })

  useEffect(() => {
    fetchSubscribers()
  }, [])

  const fetchSubscribers = async () => {
    try {
      setLoading(true)
      // In production, this would fetch from the API
      // const response = await fetch('/api/admin/newsletter/subscribers')
      // const data = await response.json()
      
      // Mock data for demonstration
      const mockData = [
        {
          id: '1',
          email: '<EMAIL>',
          name: 'John Doe',
          isActive: true,
          source: 'homepage',
          subscribedAt: '2024-12-15T10:30:00Z'
        },
        {
          id: '2',
          email: '<EMAIL>',
          name: 'Jane Smith',
          isActive: true,
          source: 'contact-form',
          subscribedAt: '2024-12-14T15:45:00Z'
        },
        {
          id: '3',
          email: '<EMAIL>',
          name: 'Bob Wilson',
          isActive: false,
          source: 'homepage',
          subscribedAt: '2024-12-10T09:15:00Z',
          unsubscribedAt: '2024-12-18T14:20:00Z'
        }
      ]
      
      setSubscribers(mockData)
      setStats({
        total: mockData.length,
        active: mockData.filter(s => s.isActive).length,
        inactive: mockData.filter(s => !s.isActive).length,
        thisMonth: mockData.filter(s => 
          new Date(s.subscribedAt).getMonth() === new Date().getMonth()
        ).length
      })
    } catch (error) {
      console.error('Failed to fetch subscribers:', error)
      toast.error('Failed to load newsletter subscribers')
    } finally {
      setLoading(false)
    }
  }

  const filteredSubscribers = subscribers.filter(subscriber => {
    const matchesSearch = subscriber.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         subscriber.name?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && subscriber.isActive) ||
                         (statusFilter === 'inactive' && !subscriber.isActive)
    const matchesSource = sourceFilter === 'all' || subscriber.source === sourceFilter
    
    return matchesSearch && matchesStatus && matchesSource
  })

  const exportSubscribers = () => {
    const csvContent = [
      ['Email', 'Name', 'Status', 'Source', 'Subscribed Date', 'Unsubscribed Date'],
      ...filteredSubscribers.map(sub => [
        sub.email,
        sub.name || '',
        sub.isActive ? 'Active' : 'Inactive',
        sub.source || '',
        new Date(sub.subscribedAt).toLocaleDateString(),
        sub.unsubscribedAt ? new Date(sub.unsubscribedAt).toLocaleDateString() : ''
      ])
    ].map(row => row.join(',')).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `newsletter-subscribers-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
    toast.success('Subscribers exported successfully')
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Newsletter Management</h1>
          <p className="text-gray-600">Manage newsletter subscribers and campaigns</p>
        </div>
        <Button onClick={exportSubscribers} className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          Export Subscribers
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Subscribers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subscribers</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unsubscribed</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.inactive}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.thisMonth}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Subscriber List</CardTitle>
          <CardDescription>View and manage all newsletter subscribers</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by email or name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sourceFilter} onValueChange={setSourceFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by source" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sources</SelectItem>
                <SelectItem value="homepage">Homepage</SelectItem>
                <SelectItem value="contact-form">Contact Form</SelectItem>
                <SelectItem value="quote-form">Quote Form</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {loading ? (
            <div className="text-center py-8">Loading subscribers...</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Email</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Source</TableHead>
                  <TableHead>Subscribed</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSubscribers.map((subscriber) => (
                  <TableRow key={subscriber.id}>
                    <TableCell className="font-medium">{subscriber.email}</TableCell>
                    <TableCell>{subscriber.name || '-'}</TableCell>
                    <TableCell>
                      <Badge variant={subscriber.isActive ? 'default' : 'secondary'}>
                        {subscriber.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell className="capitalize">{subscriber.source || '-'}</TableCell>
                    <TableCell>{new Date(subscriber.subscribedAt).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
          
          {filteredSubscribers.length === 0 && !loading && (
            <div className="text-center py-8 text-gray-500">
              No subscribers found matching your criteria
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
