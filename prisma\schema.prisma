// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User authentication and roles
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  role      UserRole @default(CUSTOMER)
  avatar    String?
  phone     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  customer Customer?
  sessions Session[]
  auditLogs AuditLog[]

  @@map("users")
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// Customer information
model Customer {
  id             String   @id @default(cuid())
  userId         String   @unique
  companyName    String?
  address        String?
  city           String?
  country        String?
  postalCode     String?
  taxId          String?
  creditLimit    Decimal? @db.Decimal(10, 2)
  paymentTerms   Int?     // Days
  preferredCurrency String @default("USD")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  shipments Shipment[]
  quotes    Quote[]
  invoices  Invoice[]

  @@map("customers")
}

// Services management
model Service {
  id          String      @id @default(cuid())
  name        String
  description String?
  category    ServiceCategory
  basePrice   Decimal     @db.Decimal(10, 2)
  currency    String      @default("USD")
  isActive    Boolean     @default(true)
  features    Json?       // JSON array of features
  regions     String[]    // Available regions
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  pricingRules PricingRule[]
  shipments    Shipment[]
  quoteItems   QuoteItem[]

  @@map("services")
}

// Dynamic pricing rules
model PricingRule {
  id        String   @id @default(cuid())
  serviceId String
  name      String
  conditions Json    // Pricing conditions (weight, distance, etc.)
  multiplier Decimal @db.Decimal(5, 4)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  service Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  @@map("pricing_rules")
}

// Shipment tracking
model Shipment {
  id              String         @id @default(cuid())
  trackingNumber  String         @unique
  customerId      String
  serviceId       String
  status          ShipmentStatus @default(PENDING)
  origin          String
  destination     String
  weight          Decimal?       @db.Decimal(8, 2)
  dimensions      Json?          // {length, width, height}
  value           Decimal?       @db.Decimal(10, 2)
  currency        String         @default("USD")
  estimatedDelivery DateTime?
  actualDelivery  DateTime?
  notes           String?
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  // Relations
  customer     Customer           @relation(fields: [customerId], references: [id])
  service      Service            @relation(fields: [serviceId], references: [id])
  statusHistory ShipmentStatus[]
  documents    ShipmentDocument[]
  invoice      Invoice?

  @@map("shipments")
}

// Shipment documents
model ShipmentDocument {
  id         String   @id @default(cuid())
  shipmentId String
  name       String
  type       DocumentType
  url        String
  size       Int?
  uploadedAt DateTime @default(now())

  shipment Shipment @relation(fields: [shipmentId], references: [id], onDelete: Cascade)

  @@map("shipment_documents")
}

// Quote system
model Quote {
  id         String      @id @default(cuid())
  customerId String
  status     QuoteStatus @default(PENDING)
  validUntil DateTime
  totalAmount Decimal    @db.Decimal(10, 2)
  currency   String      @default("USD")
  notes      String?
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt

  // Relations
  customer Customer   @relation(fields: [customerId], references: [id])
  items    QuoteItem[]
  invoice  Invoice?

  @@map("quotes")
}

model QuoteItem {
  id          String  @id @default(cuid())
  quoteId     String
  serviceId   String
  description String
  quantity    Int     @default(1)
  unitPrice   Decimal @db.Decimal(10, 2)
  totalPrice  Decimal @db.Decimal(10, 2)

  quote   Quote   @relation(fields: [quoteId], references: [id], onDelete: Cascade)
  service Service @relation(fields: [serviceId], references: [id])

  @@map("quote_items")
}

// Invoice and payment system
model Invoice {
  id            String        @id @default(cuid())
  invoiceNumber String        @unique
  customerId    String
  quoteId       String?       @unique
  shipmentId    String?       @unique
  status        InvoiceStatus @default(DRAFT)
  issueDate     DateTime      @default(now())
  dueDate       DateTime
  paidDate      DateTime?
  subtotal      Decimal       @db.Decimal(10, 2)
  taxAmount     Decimal       @db.Decimal(10, 2) @default(0)
  totalAmount   Decimal       @db.Decimal(10, 2)
  currency      String        @default("USD")
  notes         String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  customer Customer  @relation(fields: [customerId], references: [id])
  quote    Quote?    @relation(fields: [quoteId], references: [id])
  shipment Shipment? @relation(fields: [shipmentId], references: [id])
  items    InvoiceItem[]
  payments Payment[]

  @@map("invoices")
}

model InvoiceItem {
  id          String  @id @default(cuid())
  invoiceId   String
  description String
  quantity    Int     @default(1)
  unitPrice   Decimal @db.Decimal(10, 2)
  totalPrice  Decimal @db.Decimal(10, 2)

  invoice Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@map("invoice_items")
}

model Payment {
  id            String        @id @default(cuid())
  invoiceId     String
  amount        Decimal       @db.Decimal(10, 2)
  currency      String        @default("USD")
  method        PaymentMethod
  status        PaymentStatus @default(PENDING)
  transactionId String?
  paidAt        DateTime?
  createdAt     DateTime      @default(now())

  invoice Invoice @relation(fields: [invoiceId], references: [id])

  @@map("payments")
}

// Notifications
model Notification {
  id        String           @id @default(cuid())
  userId    String?
  type      NotificationType
  title     String
  message   String
  isRead    Boolean          @default(false)
  data      Json?            // Additional notification data
  createdAt DateTime         @default(now())

  @@map("notifications")
}

// Audit logging
model AuditLog {
  id        String   @id @default(cuid())
  userId    String?
  action    String
  resource  String
  resourceId String?
  oldValues Json?
  newValues Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  user User? @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

// Enums
enum UserRole {
  ADMIN
  STAFF
  CUSTOMER
}

enum ServiceCategory {
  AIR_FREIGHT
  SEA_FREIGHT
  ROAD_TRANSPORT
  CUSTOMS_CLEARANCE
  WAREHOUSING
  CONSULTANCY
}

enum ShipmentStatus {
  PENDING
  CONFIRMED
  IN_TRANSIT
  CUSTOMS_CLEARANCE
  OUT_FOR_DELIVERY
  DELIVERED
  CANCELLED
}

enum DocumentType {
  INVOICE
  PACKING_LIST
  BILL_OF_LADING
  CUSTOMS_DECLARATION
  INSURANCE_CERTIFICATE
  OTHER
}

enum QuoteStatus {
  PENDING
  APPROVED
  REJECTED
  EXPIRED
  CONVERTED
}

enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
}

enum PaymentMethod {
  CREDIT_CARD
  BANK_TRANSFER
  PAYPAL
  STRIPE
  CASH
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum NotificationType {
  SHIPMENT_UPDATE
  PAYMENT_RECEIVED
  INVOICE_DUE
  QUOTE_APPROVED
  SYSTEM_ALERT
}
