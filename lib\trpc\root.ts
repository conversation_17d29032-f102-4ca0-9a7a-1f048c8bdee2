import { createTRPCRouter } from './server'
import { authRouter } from './routers/auth'
import { shipmentsRouter } from './routers/shipments'
import { servicesRouter } from './routers/services'
import { invoicesRouter } from './routers/invoices'
import { documentsRouter } from './routers/documents'

export const appRouter = createTRPCRouter({
  auth: authRouter,
  shipments: shipmentsRouter,
  services: servicesRouter,
  invoices: invoicesRouter,
  documents: documentsRouter,
})

export type AppRouter = typeof appRouter
