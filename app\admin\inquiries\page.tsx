"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { MessageSquare, Clock, CheckCircle, AlertTriangle, Search, Mail, Phone } from 'lucide-react'
import { toast } from 'sonner'

interface ContactInquiry {
  id: string
  name: string
  email: string
  phone?: string
  company?: string
  subject: string
  message: string
  type: string
  status: string
  priority: string
  createdAt: string
  respondedAt?: string
  response?: string
}

export default function InquiriesManagementPage() {
  const [inquiries, setInquiries] = useState<ContactInquiry[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [typeFilter, setTypeFilter] = useState('all')
  const [priorityFilter, setPriorityFilter] = useState('all')
  const [selectedInquiry, setSelectedInquiry] = useState<ContactInquiry | null>(null)
  const [response, setResponse] = useState('')
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    inProgress: 0,
    resolved: 0
  })

  useEffect(() => {
    fetchInquiries()
  }, [])

  const fetchInquiries = async () => {
    try {
      setLoading(true)
      // In production, this would fetch from the API
      // const response = await fetch('/api/admin/inquiries')
      // const data = await response.json()
      
      // Mock data for demonstration
      const mockData = [
        {
          id: 'INQ-001',
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+************',
          company: 'ABC Trading',
          subject: 'Shipping inquiry for electronics',
          message: 'I need to ship electronics from Banjul to Dakar. What are your rates?',
          type: 'QUOTE',
          status: 'PENDING',
          priority: 'HIGH',
          createdAt: '2024-12-19T10:30:00Z'
        },
        {
          id: 'INQ-002',
          name: 'Jane Smith',
          email: '<EMAIL>',
          company: 'XYZ Logistics',
          subject: 'Partnership opportunity',
          message: 'We are interested in establishing a partnership for cross-border logistics.',
          type: 'PARTNERSHIP',
          status: 'IN_PROGRESS',
          priority: 'NORMAL',
          createdAt: '2024-12-18T15:45:00Z'
        },
        {
          id: 'INQ-003',
          name: 'Bob Wilson',
          email: '<EMAIL>',
          subject: 'Tracking issue',
          message: 'I cannot track my shipment with tracking number TRK123456.',
          type: 'SUPPORT',
          status: 'RESOLVED',
          priority: 'URGENT',
          createdAt: '2024-12-17T09:15:00Z',
          respondedAt: '2024-12-17T11:30:00Z',
          response: 'Issue resolved. Tracking system was temporarily down.'
        }
      ]
      
      setInquiries(mockData)
      setStats({
        total: mockData.length,
        pending: mockData.filter(i => i.status === 'PENDING').length,
        inProgress: mockData.filter(i => i.status === 'IN_PROGRESS').length,
        resolved: mockData.filter(i => i.status === 'RESOLVED').length
      })
    } catch (error) {
      console.error('Failed to fetch inquiries:', error)
      toast.error('Failed to load inquiries')
    } finally {
      setLoading(false)
    }
  }

  const filteredInquiries = inquiries.filter(inquiry => {
    const matchesSearch = inquiry.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         inquiry.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         inquiry.subject.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || inquiry.status === statusFilter
    const matchesType = typeFilter === 'all' || inquiry.type === typeFilter
    const matchesPriority = priorityFilter === 'all' || inquiry.priority === priorityFilter
    
    return matchesSearch && matchesStatus && matchesType && matchesPriority
  })

  const updateInquiryStatus = async (inquiryId: string, newStatus: string) => {
    try {
      // In production, this would update via API
      setInquiries(prev => prev.map(inquiry => 
        inquiry.id === inquiryId 
          ? { ...inquiry, status: newStatus, respondedAt: newStatus === 'RESOLVED' ? new Date().toISOString() : inquiry.respondedAt }
          : inquiry
      ))
      toast.success('Inquiry status updated successfully')
    } catch (error) {
      toast.error('Failed to update inquiry status')
    }
  }

  const submitResponse = async () => {
    if (!selectedInquiry || !response.trim()) return

    try {
      // In production, this would submit via API
      setInquiries(prev => prev.map(inquiry => 
        inquiry.id === selectedInquiry.id 
          ? { ...inquiry, response, respondedAt: new Date().toISOString(), status: 'RESOLVED' }
          : inquiry
      ))
      setResponse('')
      setSelectedInquiry(null)
      toast.success('Response sent successfully')
    } catch (error) {
      toast.error('Failed to send response')
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-red-100 text-red-800'
      case 'HIGH': return 'bg-orange-100 text-orange-800'
      case 'NORMAL': return 'bg-blue-100 text-blue-800'
      case 'LOW': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'IN_PROGRESS': return 'bg-blue-100 text-blue-800'
      case 'RESOLVED': return 'bg-green-100 text-green-800'
      case 'CLOSED': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Contact Inquiries</h1>
          <p className="text-gray-600">Manage and respond to customer inquiries</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Inquiries</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.inProgress}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Resolved</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.resolved}</div>
          </CardContent>
        </Card>
      </div>

      {/* Inquiries List */}
      <Card>
        <CardHeader>
          <CardTitle>Inquiry Management</CardTitle>
          <CardDescription>View and respond to customer inquiries</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search inquiries..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                <SelectItem value="RESOLVED">Resolved</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="GENERAL">General</SelectItem>
                <SelectItem value="QUOTE">Quote</SelectItem>
                <SelectItem value="SUPPORT">Support</SelectItem>
                <SelectItem value="PARTNERSHIP">Partnership</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="URGENT">Urgent</SelectItem>
                <SelectItem value="HIGH">High</SelectItem>
                <SelectItem value="NORMAL">Normal</SelectItem>
                <SelectItem value="LOW">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {loading ? (
            <div className="text-center py-8">Loading inquiries...</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Subject</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredInquiries.map((inquiry) => (
                  <TableRow key={inquiry.id}>
                    <TableCell className="font-mono text-sm">{inquiry.id}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{inquiry.name}</div>
                        <div className="text-sm text-gray-500">{inquiry.email}</div>
                      </div>
                    </TableCell>
                    <TableCell className="max-w-xs truncate">{inquiry.subject}</TableCell>
                    <TableCell>
                      <Badge variant="outline">{inquiry.type}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getPriorityColor(inquiry.priority)}>
                        {inquiry.priority}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(inquiry.status)}>
                        {inquiry.status.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>{new Date(inquiry.createdAt).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm" onClick={() => setSelectedInquiry(inquiry)}>
                              View
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>Inquiry Details - {inquiry.id}</DialogTitle>
                              <DialogDescription>
                                Submitted on {new Date(inquiry.createdAt).toLocaleString()}
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium">Customer</label>
                                  <p>{inquiry.name}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Company</label>
                                  <p>{inquiry.company || 'N/A'}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Email</label>
                                  <p className="flex items-center gap-2">
                                    {inquiry.email}
                                    <Button variant="ghost" size="sm" asChild>
                                      <a href={`mailto:${inquiry.email}`}>
                                        <Mail className="h-4 w-4" />
                                      </a>
                                    </Button>
                                  </p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Phone</label>
                                  <p className="flex items-center gap-2">
                                    {inquiry.phone || 'N/A'}
                                    {inquiry.phone && (
                                      <Button variant="ghost" size="sm" asChild>
                                        <a href={`tel:${inquiry.phone}`}>
                                          <Phone className="h-4 w-4" />
                                        </a>
                                      </Button>
                                    )}
                                  </p>
                                </div>
                              </div>
                              
                              <div>
                                <label className="text-sm font-medium">Subject</label>
                                <p>{inquiry.subject}</p>
                              </div>
                              
                              <div>
                                <label className="text-sm font-medium">Message</label>
                                <p className="whitespace-pre-wrap bg-gray-50 p-3 rounded">{inquiry.message}</p>
                              </div>
                              
                              {inquiry.response && (
                                <div>
                                  <label className="text-sm font-medium">Response</label>
                                  <p className="whitespace-pre-wrap bg-green-50 p-3 rounded">{inquiry.response}</p>
                                  <p className="text-sm text-gray-500 mt-1">
                                    Responded on {inquiry.respondedAt ? new Date(inquiry.respondedAt).toLocaleString() : 'N/A'}
                                  </p>
                                </div>
                              )}
                              
                              {inquiry.status !== 'RESOLVED' && (
                                <div>
                                  <label className="text-sm font-medium">Response</label>
                                  <Textarea
                                    value={response}
                                    onChange={(e) => setResponse(e.target.value)}
                                    placeholder="Type your response here..."
                                    rows={4}
                                  />
                                  <div className="flex gap-2 mt-2">
                                    <Button onClick={submitResponse}>Send Response</Button>
                                    <Button 
                                      variant="outline" 
                                      onClick={() => updateInquiryStatus(inquiry.id, 'IN_PROGRESS')}
                                    >
                                      Mark In Progress
                                    </Button>
                                  </div>
                                </div>
                              )}
                            </div>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
          
          {filteredInquiries.length === 0 && !loading && (
            <div className="text-center py-8 text-gray-500">
              No inquiries found matching your criteria
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
