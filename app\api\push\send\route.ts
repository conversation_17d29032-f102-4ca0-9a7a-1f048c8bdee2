import { type NextRequest, NextResponse } from "next/server"
import webpush from "web-push"

// Configure web-push with VAPID keys
webpush.setVapidDetails(
  "mailto:<EMAIL>",
  process.env.VAPID_PUBLIC_KEY || "",
  process.env.VAPID_PRIVATE_KEY || "",
)

export async function POST(request: NextRequest) {
  try {
    const { subscription, payload, options } = await request.json()

    if (!subscription) {
      return NextResponse.json({ success: false, message: "Subscription is required" }, { status: 400 })
    }

    const result = await webpush.sendNotification(subscription, JSON.stringify(payload), options)

    return NextResponse.json({
      success: true,
      message: "Notification sent successfully",
      result,
    })
  } catch (error) {
    console.error("Error sending push notification:", error)
    return NextResponse.json({ success: false, message: "Failed to send notification" }, { status: 500 })
  }
}
