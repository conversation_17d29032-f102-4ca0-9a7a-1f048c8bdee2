import type { Metada<PERSON> } from "next"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Plus, Minus, Save, Send, Eye } from "lucide-react"

export const metadata: Metadata = {
  title: "Create Invoice | DCF Logistics Admin",
  description: "Create a new invoice for DCF Logistics services",
}

export default function CreateInvoicePage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create New Invoice</h1>
          <p className="text-muted-foreground">Generate a new invoice for logistics services</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Eye className="mr-2 h-4 w-4" />
            Preview
          </Button>
          <Button variant="outline">
            <Save className="mr-2 h-4 w-4" />
            Save Draft
          </Button>
          <Button>
            <Send className="mr-2 h-4 w-4" />
            Send Invoice
          </Button>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Invoice Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Information</CardTitle>
              <CardDescription>Select or add customer details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="customer">Customer</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select customer" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="acme">Acme Corporation</SelectItem>
                      <SelectItem value="global">Global Traders Ltd</SelectItem>
                      <SelectItem value="tech">Tech Solutions Inc</SelectItem>
                      <SelectItem value="maritime">Maritime Logistics</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact">Contact Person</Label>
                  <Input id="contact" placeholder="Contact person name" />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="billing-address">Billing Address</Label>
                <Textarea id="billing-address" placeholder="Enter billing address" rows={3} />
              </div>
            </CardContent>
          </Card>

          {/* Invoice Details */}
          <Card>
            <CardHeader>
              <CardTitle>Invoice Details</CardTitle>
              <CardDescription>Invoice information and dates</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="invoice-number">Invoice Number</Label>
                  <Input id="invoice-number" value="INV-2025-006" readOnly />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="invoice-date">Invoice Date</Label>
                  <Input id="invoice-date" type="date" defaultValue="2025-05-25" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="due-date">Due Date</Label>
                  <Input id="due-date" type="date" />
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="payment-terms">Payment Terms</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select payment terms" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="net15">Net 15 days</SelectItem>
                      <SelectItem value="net30">Net 30 days</SelectItem>
                      <SelectItem value="net45">Net 45 days</SelectItem>
                      <SelectItem value="net60">Net 60 days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="currency">Currency</Label>
                  <Select defaultValue="usd">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="usd">USD ($)</SelectItem>
                      <SelectItem value="eur">EUR (€)</SelectItem>
                      <SelectItem value="gbp">GBP (£)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Service Items */}
          <Card>
            <CardHeader>
              <CardTitle>Service Items</CardTitle>
              <CardDescription>Add services and charges</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Service Item 1 */}
              <div className="grid gap-4 md:grid-cols-12 items-end">
                <div className="md:col-span-4 space-y-2">
                  <Label>Service</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select service" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="air-freight">Air Freight</SelectItem>
                      <SelectItem value="ground-transport">Ground Transport</SelectItem>
                      <SelectItem value="customs-clearance">Customs Clearance</SelectItem>
                      <SelectItem value="warehousing">Warehousing</SelectItem>
                      <SelectItem value="ocean-freight">Ocean Freight</SelectItem>
                      <SelectItem value="freight-forwarding">Freight Forwarding</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="md:col-span-3 space-y-2">
                  <Label>Description</Label>
                  <Input placeholder="Service description" />
                </div>
                <div className="md:col-span-2 space-y-2">
                  <Label>Quantity</Label>
                  <Input type="number" placeholder="1" defaultValue="1" />
                </div>
                <div className="md:col-span-2 space-y-2">
                  <Label>Rate</Label>
                  <Input type="number" placeholder="0.00" />
                </div>
                <div className="md:col-span-1 flex items-center justify-center">
                  <Button variant="outline" size="icon">
                    <Minus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <Separator />

              {/* Add Item Button */}
              <Button variant="outline" className="w-full">
                <Plus className="mr-2 h-4 w-4" />
                Add Service Item
              </Button>

              {/* Totals */}
              <div className="space-y-2 pt-4">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>$0.00</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax (10%):</span>
                  <span>$0.00</span>
                </div>
                <Separator />
                <div className="flex justify-between font-bold text-lg">
                  <span>Total:</span>
                  <span>$0.00</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Information */}
          <Card>
            <CardHeader>
              <CardTitle>Additional Information</CardTitle>
              <CardDescription>Notes and terms</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea id="notes" placeholder="Additional notes or comments" rows={3} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="terms">Terms & Conditions</Label>
                <Textarea
                  id="terms"
                  placeholder="Payment terms and conditions"
                  defaultValue="Payment is due within 30 days of invoice date. Late payments may incur additional charges."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Invoice Preview */}
        <div className="lg:col-span-1">
          <Card className="sticky top-6">
            <CardHeader>
              <CardTitle>Invoice Preview</CardTitle>
              <CardDescription>Live preview of your invoice</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border rounded-lg p-4 bg-gray-50">
                <div className="text-center mb-4">
                  <h3 className="font-bold text-lg">DCF Logistics</h3>
                  <p className="text-sm text-muted-foreground">Professional Logistics Services</p>
                </div>

                <div className="space-y-3 text-sm">
                  <div>
                    <div className="font-medium">Invoice #: INV-2025-006</div>
                    <div className="text-muted-foreground">Date: May 25, 2025</div>
                  </div>

                  <Separator />

                  <div>
                    <div className="font-medium">Bill To:</div>
                    <div className="text-muted-foreground">Select customer...</div>
                  </div>

                  <Separator />

                  <div>
                    <div className="font-medium">Services:</div>
                    <div className="text-muted-foreground">No items added</div>
                  </div>

                  <Separator />

                  <div className="flex justify-between font-bold">
                    <span>Total:</span>
                    <span>$0.00</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Button className="w-full">
                  <Send className="mr-2 h-4 w-4" />
                  Send Invoice
                </Button>
                <Button variant="outline" className="w-full">
                  <Save className="mr-2 h-4 w-4" />
                  Save as Draft
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
