import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const subscription = await request.json()

    // Here you would typically remove the subscription from your database
    console.log("Removing push subscription:", subscription)

    // In a real application, you would:
    // 1. Find the subscription in your database
    // 2. Remove it
    // 3. Update user preferences

    return NextResponse.json({
      success: true,
      message: "Subscription removed successfully",
    })
  } catch (error) {
    console.error("Error removing subscription:", error)
    return NextResponse.json({ success: false, message: "Failed to remove subscription" }, { status: 500 })
  }
}
