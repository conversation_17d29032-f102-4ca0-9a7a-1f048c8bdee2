"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>es<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  Upload,
  Download,
  FileText,
  CheckCircle,
  AlertCircle,
  Users,
  Database,
  RefreshCw,
  Eye,
  Trash2,
} from "lucide-react"
import { useState } from "react"

export default function CustomerImportClientPage() {
  const [importProgress, setImportProgress] = useState(0)
  const [isImporting, setIsImporting] = useState(false)

  const importHistory = [
    {
      id: 1,
      filename: "customers_2025_q1.csv",
      date: "2025-05-20",
      records: 1247,
      successful: 1198,
      failed: 49,
      status: "completed",
    },
    {
      id: 2,
      filename: "legacy_customers.xlsx",
      date: "2025-05-15",
      records: 3456,
      successful: 3401,
      failed: 55,
      status: "completed",
    },
    {
      id: 3,
      filename: "new_signups_may.csv",
      date: "2025-05-10",
      records: 89,
      successful: 89,
      failed: 0,
      status: "completed",
    },
  ]

  const handleFileUpload = () => {
    setIsImporting(true)
    setImportProgress(0)
    
    // Simulate import progress
    const interval = setInterval(() => {
      setImportProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          setIsImporting(false)
          return 100
        }
        return prev + 10
      })
    }, 500)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Customer Data Import</h1>
          <p className="text-muted-foreground">Import customer data from CSV, Excel, or other sources</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Download Template
          </Button>
          <Button variant="outline">
            <Eye className="mr-2 h-4 w-4" />
            View Import Log
          </Button>
        </div>
      </div>

      {/* Import Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Imports</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{importHistory.length}</div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Records Imported</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {importHistory.reduce((sum, imp) => sum + imp.records, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Total customer records</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(
                (importHistory.reduce((sum, imp) => sum + imp.successful, 0) / 
                 importHistory.reduce((sum, imp) => sum + imp.records, 0)) * 100
              )}%
            </div>
            <p className="text-xs text-muted-foreground">Average success rate</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failed Records</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {importHistory.reduce((sum, imp) => sum + imp.failed, 0)}
            </div>
            <p className="text-xs text-muted-foreground">Require attention</p>
          </CardContent>
        </Card>
      </div>

      {/* Import Interface */}
      <Tabs defaultValue="upload" className="space-y-4">
        <TabsList>
          <TabsTrigger value="upload">File Upload</TabsTrigger>
          <TabsTrigger value="mapping">Field Mapping</TabsTrigger>
          <TabsTrigger value="validation">Data Validation</TabsTrigger>
          <TabsTrigger value="history">Import History</TabsTrigger>
        </TabsList>

        {/* File Upload */}
        <TabsContent value="upload">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Upload Customer Data</CardTitle>
                <CardDescription>Select a file to import customer information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <div className="text-lg font-medium mb-2">Drop files here or click to browse</div>
                  <div className="text-sm text-muted-foreground mb-4">
                    Supports CSV, Excel (.xlsx), and JSON files up to 10MB
                  </div>
                  <Button>
                    <Upload className="mr-2 h-4 w-4" />
                    Choose File
                  </Button>
                </div>

                {isImporting && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Importing customers...</span>
                      <span>{importProgress}%</span>
                    </div>
                    <Progress value={importProgress} className="w-full" />
                  </div>
                )}

                <div className="space-y-2">
                  <div className="text-sm font-medium">Supported Formats:</div>
                  <div className="grid gap-2">
                    <div className="flex items-center gap-2 text-sm">
                      <FileText className="h-4 w-4" />
                      <span>CSV (Comma Separated Values)</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <FileText className="h-4 w-4" />
                      <span>Excel (.xlsx)</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <FileText className="h-4 w-4" />
                      <span>JSON (JavaScript Object Notation)</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Import Guidelines</CardTitle>
                <CardDescription>Best practices for successful data import</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <div className="text-sm">
                      <div className="font-medium">Required Fields</div>
                      <div className="text-muted-foreground">Company Name, Email, Phone</div>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <div className="text-sm">
                      <div className="font-medium">Email Format</div>
                      <div className="text-muted-foreground">Valid email addresses only</div>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <div className="text-sm">
                      <div className="font-medium">Phone Numbers</div>
                      <div className="text-muted-foreground">Include country code (+220)</div>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <div className="text-sm">
                      <div className="font-medium">Duplicate Handling</div>
                      <div className="text-muted-foreground">Existing customers will be updated</div>
                    </div>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <Button variant="outline" className="w-full">
                    <Download className="mr-2 h-4 w-4" />
                    Download Sample Template
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Field Mapping */}
        <TabsContent value="mapping">
          <Card>
            <CardHeader>
              <CardTitle>Field Mapping</CardTitle>
              <CardDescription>Map your file columns to customer database fields</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <div className="text-sm font-medium mb-2">Your File Columns</div>
                    <div className="space-y-2">
                      {['Company', 'Email Address', 'Phone Number', 'Contact Person', 'Address', 'City', 'Country'].map((col, index) => (
                        <div key={index} className="p-2 border rounded bg-gray-50 text-sm">
                          {col}
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-sm font-medium mb-2">Database Fields</div>
                    <div className="space-y-2">
                      {['company_name', 'email', 'phone', 'contact_name', 'address', 'city', 'country'].map((field, index) => (
                        <div key={index} className="p-2 border rounded bg-blue-50 text-sm">
                          {field}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="flex gap-2 pt-4">
                  <Button>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Confirm Mapping
                  </Button>
                  <Button variant="outline">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Auto-Map Fields
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Data Validation */}
        <TabsContent value="validation">
          <Card>
            <CardHeader>
              <CardTitle>Data Validation</CardTitle>
              <CardDescription>Review and validate imported data before saving</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-green-600">1,198</div>
                    <div className="text-sm text-muted-foreground">Valid Records</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-red-600">49</div>
                    <div className="text-sm text-muted-foreground">Invalid Records</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-amber-600">23</div>
                    <div className="text-sm text-muted-foreground">Duplicates Found</div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="text-sm font-medium">Validation Issues:</div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 p-2 border rounded-lg bg-red-50">
                      <AlertCircle className="h-4 w-4 text-red-500" />
                      <span className="text-sm">15 records have invalid email formats</span>
                    </div>
                    <div className="flex items-center gap-2 p-2 border rounded-lg bg-red-50">
                      <AlertCircle className="h-4 w-4 text-red-500" />
                      <span className="text-sm">12 records are missing required phone numbers</span>
                    </div>
                    <div className="flex items-center gap-2 p-2 border rounded-lg bg-amber-50">
                      <AlertCircle className="h-4 w-4 text-amber-500" />
                      <span className="text-sm">23 records match existing customers</span>
                    </div>
                  </div>
                </div>

                <div className="flex gap-2 pt-4">
                  <Button>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Import Valid Records
                  </Button>
                  <Button variant="outline">
                    <Download className="mr-2 h-4 w-4" />
                    Export Error Report
                  </Button>
                </div>
              </CardContent>
          </Card>
        </TabsContent>

        {/* Import History */}
        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Import History</CardTitle>
              <CardDescription>Previous customer data imports</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {importHistory.map((import_record) => (
                  <div key={import_record.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <FileText className="h-8 w-8 text-blue-500" />
                      <div>
                        <div className="font-medium">{import_record.filename}</div>
                        <div className="text-sm text-muted-foreground">
                          {import_record.date} • {import_record.records.toLocaleString()} records
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <div className="text-sm">
                          <span className="text-green-600 font-medium">{import_record.successful}</span> successful
                        </div>
                        {import_record.failed > 0 && (
                          <div className="text-sm">
                            <span className="text-red-600 font-medium">{import_record.failed}</span> failed
                          </div>
                        )}
                      </div>
                      
                      <Badge variant={import_record.status === 'completed' ? 'default' : 'secondary'}>
                        {import_record.status}
                      </Badge>
                      
                      <div className="flex gap-1">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
