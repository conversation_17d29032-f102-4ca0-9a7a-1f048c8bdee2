import type { <PERSON><PERSON><PERSON> } from "next"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import DashboardSidebar from "@/components/account/dashboard-sidebar"
import { Package, Clock, FileText, Bell, TrendingUp, Calendar } from "lucide-react"

export const metadata: Metadata = {
  title: "Account Dashboard | DCF Logistics",
  description: "Manage your shipments and logistics services",
}

export default function DashboardPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <div className="flex-1 flex">
        <aside className="hidden md:flex w-64 flex-col border-r bg-gray-50/50 min-h-screen">
          <DashboardSidebar />
        </aside>
        <main className="flex-1 p-6">
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
              <p className="text-muted-foreground">Welcome back to your DCF Logistics client portal</p>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Shipments</CardTitle>
                  <Package className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">4</div>
                  <p className="text-xs text-muted-foreground">Currently in transit</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Pending Shipments</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">2</div>
                  <p className="text-xs text-muted-foreground">Awaiting processing</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Documents</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">12</div>
                  <p className="text-xs text-muted-foreground">Available for download</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Notifications</CardTitle>
                  <Bell className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">3</div>
                  <p className="text-xs text-muted-foreground">Unread alerts</p>
                </CardContent>
              </Card>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <Card className="col-span-1">
                <CardHeader>
                  <CardTitle>Recent Shipments</CardTitle>
                  <CardDescription>Overview of your latest shipments</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="border-b pb-4">
                      <div className="flex justify-between items-center mb-1">
                        <div className="font-medium">DCF-10234</div>
                        <div className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">In Transit</div>
                      </div>
                      <div className="text-sm text-muted-foreground mb-1">Banjul to Lagos</div>
                      <div className="flex justify-between text-xs">
                        <span>Estimated delivery: May 25, 2025</span>
                        <Link href="/tracking/DCF-10234" className="text-primary hover:underline">
                          Track
                        </Link>
                      </div>
                    </div>

                    <div className="border-b pb-4">
                      <div className="flex justify-between items-center mb-1">
                        <div className="font-medium">DCF-10233</div>
                        <div className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Processing</div>
                      </div>
                      <div className="text-sm text-muted-foreground mb-1">Accra to Dakar</div>
                      <div className="flex justify-between text-xs">
                        <span>Estimated delivery: May 28, 2025</span>
                        <Link href="/tracking/DCF-10233" className="text-primary hover:underline">
                          Track
                        </Link>
                      </div>
                    </div>

                    <div className="border-b pb-4">
                      <div className="flex justify-between items-center mb-1">
                        <div className="font-medium">DCF-10232</div>
                        <div className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">Delivered</div>
                      </div>
                      <div className="text-sm text-muted-foreground mb-1">Dakar to Banjul</div>
                      <div className="flex justify-between text-xs">
                        <span>Delivered: May 18, 2025</span>
                        <Link href="/tracking/DCF-10232" className="text-primary hover:underline">
                          Details
                        </Link>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <div className="font-medium">DCF-10231</div>
                        <div className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">Delivered</div>
                      </div>
                      <div className="text-sm text-muted-foreground mb-1">Lagos to Accra</div>
                      <div className="flex justify-between text-xs">
                        <span>Delivered: May 15, 2025</span>
                        <Link href="/tracking/DCF-10231" className="text-primary hover:underline">
                          Details
                        </Link>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 text-center">
                    <Button variant="outline" size="sm" asChild>
                      <Link href="/account/shipments">View All Shipments</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card className="col-span-1">
                <CardHeader>
                  <CardTitle>Upcoming Shipments</CardTitle>
                  <CardDescription>Scheduled shipments for the next 30 days</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-start gap-4 border-b pb-4">
                      <div className="bg-primary/10 p-2 rounded-md">
                        <Calendar className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">Air Freight: Banjul to Accra</div>
                        <div className="text-sm text-muted-foreground">Scheduled for May 24, 2025</div>
                        <div className="text-xs text-muted-foreground mt-1">Reference: DCF-10235</div>
                      </div>
                    </div>

                    <div className="flex items-start gap-4 border-b pb-4">
                      <div className="bg-primary/10 p-2 rounded-md">
                        <Calendar className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">Ocean Freight: Lagos to Dakar</div>
                        <div className="text-sm text-muted-foreground">Scheduled for May 30, 2025</div>
                        <div className="text-xs text-muted-foreground mt-1">Reference: DCF-10236</div>
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className="bg-primary/10 p-2 rounded-md">
                        <Calendar className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">Road Transport: Accra to Banjul</div>
                        <div className="text-sm text-muted-foreground">Scheduled for June 5, 2025</div>
                        <div className="text-xs text-muted-foreground mt-1">Reference: DCF-10237</div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 text-center">
                    <Button variant="outline" size="sm" asChild>
                      <Link href="/quote">Book New Shipment</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <Card className="col-span-2">
                <CardHeader>
                  <CardTitle>Shipping Activity</CardTitle>
                  <CardDescription>Your shipping volume over the past 6 months</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[200px] flex items-center justify-center border rounded-md bg-muted/20">
                    <TrendingUp className="h-8 w-8 text-muted-foreground" />
                    <span className="ml-2 text-muted-foreground">Shipping activity chart</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Documents</CardTitle>
                  <CardDescription>Latest shipping documents</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <div className="text-sm font-medium">Invoice - DCF-10234</div>
                      <div className="ml-auto">
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="text-primary"
                          >
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7 10 12 15 17 10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                          </svg>
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <div className="text-sm font-medium">Bill of Lading - DCF-10233</div>
                      <div className="ml-auto">
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="text-primary"
                          >
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7 10 12 15 17 10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                          </svg>
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <div className="text-sm font-medium">Customs Declaration - DCF-10234</div>
                      <div className="ml-auto">
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="text-primary"
                          >
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7 10 12 15 17 10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                          </svg>
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 text-center">
                    <Button variant="outline" size="sm" asChild>
                      <Link href="/account/documents">View All Documents</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
